<?xml version="1.0" encoding="UTF-8"?>
<DataSourceObject xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <creationType>Custom</creationType>
    <dataSource>SCRIBA_DLOStorico_MCE</dataSource>
    <dataSourceFields>
        <fullName>BatchID</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>BatchID</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_BatchID</keyQualifierName>
        <masterLabel>BatchID</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>CANALE</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>CANALE</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_CANALE</keyQualifierName>
        <masterLabel>CANALE</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Categoria_EVENTO</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Categoria_EVENTO</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Categoria_EVENTO</keyQualifierName>
        <masterLabel>Categoria_EVENTO</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>cdp_sys_PartitionDate</fullName>
        <datatype>F</datatype>
        <definitionCreationType>System</definitionCreationType>
        <externalName>cdp_sys_PartitionDate</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_cdp_sys_PartitionDate</keyQualifierName>
        <masterLabel>cdp_sys_PartitionDate</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber>1.0</versionNumber>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>cdp_sys_SourceVersion</fullName>
        <datatype>S</datatype>
        <definitionCreationType>System</definitionCreationType>
        <externalName>cdp_sys_SourceVersion</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_cdp_sys_SourceVersion</keyQualifierName>
        <masterLabel>cdp_sys_SourceVersion</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Citt_IP</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Citt_IP</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Citt_IP</keyQualifierName>
        <masterLabel>Città_IP</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>ContactKey</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>ContactKey</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_ContactKey</keyQualifierName>
        <masterLabel>ContactKey</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Data_APERTURA</fullName>
        <datatype>F</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Data_APERTURA</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Data_APERTURA</keyQualifierName>
        <masterLabel>Data_APERTURA</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber>1.0</versionNumber>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Data_CLICK</fullName>
        <datatype>F</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Data_CLICK</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Data_CLICK</keyQualifierName>
        <masterLabel>Data_CLICK</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber>1.0</versionNumber>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Data_EVENTO</fullName>
        <datatype>F</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Data_EVENTO</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Data_EVENTO</keyQualifierName>
        <masterLabel>Data_EVENTO</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber>1.0</versionNumber>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Data_EVENTO_dateformat</fullName>
        <datatype>F</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Data_EVENTO_dateformat</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>true</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Data_EVENTO_dateformat</keyQualifierName>
        <masterLabel>Data_EVENTO_dateformat</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber>1.0</versionNumber>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Data_INVIO</fullName>
        <datatype>F</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Data_INVIO</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Data_INVIO</keyQualifierName>
        <masterLabel>Data_INVIO</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber>1.0</versionNumber>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>DataSource</fullName>
        <datatype>S</datatype>
        <definitionCreationType>System</definitionCreationType>
        <externalName>DataSource</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_DataSource</keyQualifierName>
        <masterLabel>Data Source</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>DataSourceObject</fullName>
        <datatype>S</datatype>
        <definitionCreationType>System</definitionCreationType>
        <externalName>DataSourceObject</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_DataSourceObject</keyQualifierName>
        <masterLabel>Data Source Object</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>DES_CRMO_COD_TIPO</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>DES_CRMO_COD_TIPO</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_DES_CRMO_COD_TIPO</keyQualifierName>
        <masterLabel>DES_CRMO_COD_TIPO</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>DES_EVENT_CLASS</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>DES_EVENT_CLASS</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_DES_EVENT_CLASS</keyQualifierName>
        <masterLabel>DES_EVENT_CLASS</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>DES_MACRO_EVENTO</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>DES_MACRO_EVENTO</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_DES_MACRO_EVENTO</keyQualifierName>
        <masterLabel>DES_MACRO_EVENTO</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>DESC_EVENTO</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>DESC_EVENTO</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_DESC_EVENTO</keyQualifierName>
        <masterLabel>DESC_EVENTO</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Descrizione_CLIENT</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Descrizione_CLIENT</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Descrizione_CLIENT</keyQualifierName>
        <masterLabel>Descrizione_CLIENT</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Descrizione_PIATTAFORMA</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Descrizione_PIATTAFORMA</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Descrizione_PIATTAFORMA</keyQualifierName>
        <masterLabel>Descrizione_PIATTAFORMA</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Dettagli_EVENTO</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Dettagli_EVENTO</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Dettagli_EVENTO</keyQualifierName>
        <masterLabel>Dettagli_EVENTO</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>EVENT_ID</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>EVENT_ID</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_EVENT_ID</keyQualifierName>
        <masterLabel>EVENT_ID</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>ID_COMUNICAZIONE</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>ID_COMUNICAZIONE</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_ID_COMUNICAZIONE</keyQualifierName>
        <masterLabel>ID_COMUNICAZIONE</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>ID_SCRIBA</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>ID_SCRIBA</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_ID_SCRIBA</keyQualifierName>
        <masterLabel>ID_SCRIBA</masterLabel>
        <primaryIndexOrder>1</primaryIndexOrder>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>ID_TEMPLATE</fullName>
        <datatype>N</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>ID_TEMPLATE</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_ID_TEMPLATE</keyQualifierName>
        <masterLabel>ID_TEMPLATE</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Indirizzo_IP</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Indirizzo_IP</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Indirizzo_IP</keyQualifierName>
        <masterLabel>Indirizzo_IP</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>InternalOrganization</fullName>
        <datatype>S</datatype>
        <definitionCreationType>System</definitionCreationType>
        <externalName>InternalOrganization</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_InternalOrganization</keyQualifierName>
        <masterLabel>Internal Organization</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>ISP</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>ISP</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_ISP</keyQualifierName>
        <masterLabel>ISP</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>JobID</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>JobID</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_JobID</keyQualifierName>
        <masterLabel>JobID</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>ListID</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>ListID</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_ListID</keyQualifierName>
        <masterLabel>ListID</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Nazione_IP</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Nazione_IP</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Nazione_IP</keyQualifierName>
        <masterLabel>Nazione_IP</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Nome_CLIENT</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Nome_CLIENT</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Nome_CLIENT</keyQualifierName>
        <masterLabel>Nome_CLIENT</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Nome_PIATTAFORMA</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Nome_PIATTAFORMA</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Nome_PIATTAFORMA</keyQualifierName>
        <masterLabel>Nome_PIATTAFORMA</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Regione_IP</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Regione_IP</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Regione_IP</keyQualifierName>
        <masterLabel>Regione_IP</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>TAG_ID</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>TAG_ID</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_TAG_ID</keyQualifierName>
        <masterLabel>TAG_ID</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>TAG_URL</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>TAG_URL</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_TAG_URL</keyQualifierName>
        <masterLabel>TAG_URL</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>TAG_URL_DINAMICO</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>TAG_URL_DINAMICO</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_TAG_URL_DINAMICO</keyQualifierName>
        <masterLabel>TAG_URL_DINAMICO</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Timezone_IP</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Timezone_IP</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Timezone_IP</keyQualifierName>
        <masterLabel>Timezone_IP</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Tipo_CLIENT</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Tipo_CLIENT</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Tipo_CLIENT</keyQualifierName>
        <masterLabel>Tipo_CLIENT</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Tipo_EVENTO</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Tipo_EVENTO</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Tipo_EVENTO</keyQualifierName>
        <masterLabel>Tipo_EVENTO</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Tipo_PIATTAFORMA</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Tipo_PIATTAFORMA</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Tipo_PIATTAFORMA</keyQualifierName>
        <masterLabel>Tipo_PIATTAFORMA</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <masterLabel>SCRIBA_DLOStorico_MCE</masterLabel>
    <objectCategory>Engagement</objectCategory>
    <objectType>Object</objectType>
    <sourceObject>SCRIBA_DLOStorico_MCE__dll</sourceObject>
    <storageType>LOCAL</storageType>
    <templateVersion>1</templateVersion>
</DataSourceObject>
