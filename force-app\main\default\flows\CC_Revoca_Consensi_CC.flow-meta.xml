<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Copy_1_of_send_email</name>
        <label>Copy 1 of send email</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <connector>
            <targetReference>screenConfermaRevoca</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recipientAddresses</name>
        </inputParameters>
        <inputParameters>
            <name>composeEmailContent</name>
            <value>
                <stringValue>True</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <elementReference>subjectEmail</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <stringValue><PERSON><PERSON><PERSON><PERSON>, segnaliamo richiesta di unsubscribe dalle chiamate del Contact Center recepita telefonicamente.
Come da procedura vi chiediamo cortesemente di registrarla nei nostri sistemi.  
Di seguito i riferimenti del cliente e la nota dell&apos;operatore che ha ricevuto la richiesta.

nome: {!get_Attivit.Account.FirstName}
cognome: {!get_Attivit.Account.LastName}
codice fiscale: {!get_Attivit.Account.CF__c}
agenzia: {!get_Attivit.Agency__r.Name}, {!get_Attivit.Agency__r.Description}, {!get_Attivit.Agency__r.Codice_Agenzia_Madre__c}
nota: {!get_Attivit.NotesRecontact__c}

Grazie,  
Team Contact Center</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientId</name>
            <value>
                <stringValue>null</stringValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <offset>0</offset>
        <versionString>2.0.1</versionString>
    </actionCalls>
    <actionCalls>
        <name>send_email</name>
        <label>send email</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <connector>
            <targetReference>screenRichiestaEsitata</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recipientAddresses</name>
        </inputParameters>
        <inputParameters>
            <name>composeEmailContent</name>
            <value>
                <stringValue>True</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <elementReference>subjectEmail</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <stringValue>Buonasera, segnaliamo richiesta di unsubscribe dalle chiamate del Contact Center recepita telefonicamente.
Come da procedura vi chiediamo cortesemente di registrarla nei nostri sistemi.  
Di seguito i riferimenti del cliente e la nota dell&apos;operatore che ha ricevuto la richiesta.

nome: {!get_Attivit.Account.FirstName}
cognome: {!get_Attivit.Account.LastName}
codice fiscale: {!get_Attivit.Account.CF__c}
agenzia: {!get_Attivit.Agency__r.Name}, {!get_Attivit.Agency__r.Description}, {!get_Attivit.Agency__r.Codice_Agenzia_Madre__c}
nota: {!get_Attivit.NotesRecontact__c}

Grazie,  
Team Contact Center</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientId</name>
            <value>
                <stringValue>null</stringValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <offset>0</offset>
        <versionString>2.0.1</versionString>
    </actionCalls>
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>assign_email_to_send</name>
        <label>assign email to send</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>emailToSend</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_1.EmailAddress__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_1</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_1_of_modifyEsitoAttivit_2</name>
        <label>Copy 1 of modifyEsitoAttività2</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>get_Attivit.RevocationCC_Sent__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Copy_1_of_updateActivity</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_2_of_modifyEsitoAttivit2</name>
        <label>modifyEsitoAttività2</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>get_Attivit.Esito__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Esito</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_Attivit.Sottoesito__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Sottoesito</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_Attivit.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_Attivit.RevocationCC_Sent__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>updateActivity</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>modifyEsitoAttivit</name>
        <label>modifyEsitoAttività</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>get_Attivit.Esito__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Non interessato</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_Attivit.Sottoesito__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Revoca Consensi CC</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>screenConferma</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>AltroVantaggio</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;Altro Vantaggio&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Altro Vantaggio</stringValue>
        </value>
    </choices>
    <choices>
        <name>Concorrenza</name>
        <choiceText>Concorrenza</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Concorrenza</stringValue>
        </value>
    </choices>
    <choices>
        <name>Optout</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;Optout&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Optout</stringValue>
        </value>
    </choices>
    <choices>
        <name>Prezzo</name>
        <choiceText>Prezzo</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Prezzo</stringValue>
        </value>
    </choices>
    <choices>
        <name>Prodotto</name>
        <choiceText>Prodotto</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Prodotto</stringValue>
        </value>
    </choices>
    <choices>
        <name>revocaConsensoCC</name>
        <choiceText>Revoca Consensi CC</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Revoca Consensi CC</stringValue>
        </value>
    </choices>
    <choices>
        <name>UnsubscribeCC</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;Unsubscribe CC&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Unsubscribe CC</stringValue>
        </value>
    </choices>
    <choices>
        <name>valueAppuntamentoAgenzia</name>
        <choiceText>&lt;h5&gt;Appuntamento Agenzia&lt;/h5&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Appuntamento Agenzia</stringValue>
        </value>
    </choices>
    <choices>
        <name>valueDaRichiamareAgenzia</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;Da richiamare Agenzia&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Da richiamare Agenzia</stringValue>
        </value>
    </choices>
    <choices>
        <name>valueInteresseGenerico</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;Interesse generico&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Interesse generico</stringValue>
        </value>
    </choices>
    <choices>
        <name>valueNonInteressato</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;Non interessato&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Non interessato</stringValue>
        </value>
    </choices>
    <choices>
        <name>valueVendita</name>
        <choiceText>Vendita</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Vendita</stringValue>
        </value>
    </choices>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>check_inviata_revoca</name>
        <label>check inviata revoca</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>revoca_Consenso_CC</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>non inviata in precedenza</defaultConnectorLabel>
        <rules>
            <name>inviata_in_precedenza</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>get_Attivit.RevocationCC_Sent__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>revoca_gi_inviata</targetReference>
            </connector>
            <label>inviata in precedenza</label>
        </rules>
    </decisions>
    <decisions>
        <name>checkButton</name>
        <label>checkButton</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>isClickConferma</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>footerButton.renderNext</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Revoca_Consenso_CC2</targetReference>
            </connector>
            <label>isClickConferma</label>
        </rules>
    </decisions>
    <decisions>
        <name>checkDecisionButton</name>
        <label>checkDecisionButton</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Copy_1_of_modifyEsitoAttivit_2</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>isConferma</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>test.renderNext</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>modifyEsitoAttivit</targetReference>
            </connector>
            <label>isConferma</label>
        </rules>
    </decisions>
    <description>Flow che attraverso un action manda la richiesta di revoca CC</description>
    <environments>Default</environments>
    <formulas>
        <name>Ambiente</name>
        <dataType>String</dataType>
        <expression>IF(CONTAINS({!$Api.Enterprise_Server_URL_260}, &apos;sandbox&apos;), &apos;Sandbox&apos;, &apos;Production&apos;)</expression>
    </formulas>
    <formulas>
        <name>subjectEmail</name>
        <dataType>String</dataType>
        <expression>&quot;Richiesta Unsubscribe CC per cliente &quot;+{!get_account.Name}</expression>
    </formulas>
    <interviewLabel>CC- {!$Flow.CurrentDateTime}</interviewLabel>
    <label>CC-Revoca Consensi CC</label>
    <loops>
        <name>Loop_1</name>
        <label>Loop 1</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>get_metadato_email</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>assign_email_to_send</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>check_inviata_revoca</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>get_account</name>
        <label>get account</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>get_metadato_email</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>get_Attivit.AccountId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Name</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>get_Attivit</name>
        <label>get Attività</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>get_account</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>get_metadato_email</name>
        <label>get metadato email</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_1</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Label</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Ambiente</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>CC_Email_Config__mdt</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Copy_1_of_updateActivity</name>
        <label>Copy 1 of updateActivity</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Copy_1_of_send_email</targetReference>
        </connector>
        <inputReference>get_Attivit</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>updateActivity</name>
        <label>updateActivity</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>send_email</targetReference>
        </connector>
        <inputReference>get_Attivit</inputReference>
    </recordUpdates>
    <screens>
        <name>revoca_Consenso_CC</name>
        <label>revoca Consenso CC</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Annulla</backButtonLabel>
        <connector>
            <targetReference>checkButton</targetReference>
        </connector>
        <fields>
            <name>textInoltroRichiesta</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;Sei sicuro di voler inoltrare la richiesta di &lt;/span&gt;&lt;span style=&quot;font-size: 14px; color: rgb(0, 0, 0);&quot;&gt;Revoca consensi CC all&apos;ufficio di Revoca Consenso?&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <fields>
            <name>footerButton</name>
            <extensionName>runtime_industries_lending:flowFooter</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>buttonLabelCustom</name>
                <value>
                    <stringValue>Annulla</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>buttonLabelNext</name>
                <value>
                    <stringValue>Conferma</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>showPrevious</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Revoca_Consenso_CC2</name>
        <label>Revoca Consenso CC</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Annulla</backButtonLabel>
        <connector>
            <targetReference>checkDecisionButton</targetReference>
        </connector>
        <fields>
            <name>textEsitoFinaleChiamata</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;color: rgb(0, 0, 0); font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;Vuoi aggiungerlo anche come esito finale della chiamata?&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <fields>
            <name>test</name>
            <extensionName>runtime_industries_lending:flowFooter</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>buttonLabelCustom</name>
                <value>
                    <stringValue>Annulla</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>renderNext</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>buttonLabelNext</name>
                <value>
                    <stringValue>Conferma</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>buttonLabelPrevious</name>
                <value>
                    <stringValue>Previous</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>showPrevious</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>revoca_gi_inviata</name>
        <label>revoca già inviata</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>textrevocaeffettuata</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;La richiesta di Revoca è stata gia inviata sul attività &lt;/span&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;{!get_Attivit.CaseNumber}&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>screenConferma</name>
        <label>Esito Finale</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Annulla</backButtonLabel>
        <connector>
            <targetReference>Copy_2_of_modifyEsitoAttivit2</targetReference>
        </connector>
        <fields>
            <name>EsitoFinale</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;Conferma gli esiti per questa attività &lt;/span&gt;&lt;a href=&quot;/lightning/r/Case/{!get_Attivit.Id}/view&quot; rel=&quot;noopener noreferrer&quot; target=&quot;_blank&quot;&gt;{!get_Attivit.CaseNumber}&lt;/a&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <fields>
            <name>dependencyPicklistEsito</name>
            <extensionName>flowruntime:dependentPicklists</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>dependencyWrapperApiName</name>
                <value>
                    <stringValue>case</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>topPicklistApiName</name>
                <value>
                    <stringValue>Esito__c</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>middlePicklistApiName</name>
                <value>
                    <stringValue>Sottoesito__c</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>topLabel</name>
                <value>
                    <stringValue>Esito</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>topRequired</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>middleLabel</name>
                <value>
                    <stringValue>Sotto Esito</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>middleRequired</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>topValue</name>
                <value>
                    <elementReference>get_Attivit.Esito__c</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>middleValue</name>
                <value>
                    <elementReference>get_Attivit.Sottoesito__c</elementReference>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Sottoesito</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <elementReference>recordId</elementReference>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Esito</name>
            <choiceReferences>valueInteresseGenerico</choiceReferences>
            <choiceReferences>valueNonInteressato</choiceReferences>
            <choiceReferences>valueAppuntamentoAgenzia</choiceReferences>
            <choiceReferences>valueVendita</choiceReferences>
            <choiceReferences>valueDaRichiamareAgenzia</choiceReferences>
            <dataType>String</dataType>
            <defaultSelectedChoiceReference>valueNonInteressato</defaultSelectedChoiceReference>
            <fieldText>Esito</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>6</stringValue>
                </width>
            </styleProperties>
        </fields>
        <fields>
            <name>Sottoesito</name>
            <choiceReferences>Optout</choiceReferences>
            <choiceReferences>Prezzo</choiceReferences>
            <choiceReferences>Prodotto</choiceReferences>
            <choiceReferences>AltroVantaggio</choiceReferences>
            <choiceReferences>Concorrenza</choiceReferences>
            <choiceReferences>UnsubscribeCC</choiceReferences>
            <choiceReferences>revocaConsensoCC</choiceReferences>
            <dataType>String</dataType>
            <defaultSelectedChoiceReference>revocaConsensoCC</defaultSelectedChoiceReference>
            <fieldText>Sottoesito</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>6</stringValue>
                </width>
            </styleProperties>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Esito</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>Non interessato</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>screenConfermaRevoca</name>
        <label>screenConfermaRevoca</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>toastSuccessInoltro</name>
            <extensionName>c:cC_OpenToastEventIntoFlow</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>toastMessage</name>
                <value>
                    <stringValue>La richiesta è stata inoltrata correttamente</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>toastVariant</name>
                <value>
                    <stringValue>success</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <fields>
            <name>redirectHomePage1</name>
            <extensionName>c:cC_RedirectFlowRiassegnazione</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <nextOrFinishButtonLabel>Chiudi</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>screenRichiestaEsitata</name>
        <label>screenRichiestaEsitata</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>toastSuccess</name>
            <extensionName>c:cC_OpenToastEventIntoFlow</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>toastMessage</name>
                <value>
                    <stringValue>L’attività &quot;{!get_Attivit.CaseNumber}&quot; è stata chiusa correttamente</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>toastVariant</name>
                <value>
                    <stringValue>success</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <fields>
            <name>redirectToHomePage</name>
            <extensionName>c:cC_RedirectFlowRiassegnazione</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <nextOrFinishButtonLabel>Chiudi</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>get_Attivit</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>emailToSend</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
