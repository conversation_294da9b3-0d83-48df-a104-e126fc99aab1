<?xml version="1.0" encoding="UTF-8"?>
<DataKitObjectTemplate xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <developerName xsi:nil="true"/>
    <entityPayload>{&quot;definition&quot;:{&quot;nodes&quot;:{&quot;OUTPUT2&quot;:{&quot;sources&quot;:[&quot;JOIN0&quot;],&quot;action&quot;:&quot;outputD360&quot;,&quot;parameters&quot;:{&quot;fieldsMappings&quot;:[{&quot;sourceField&quot;:&quot;TAG_URL_DINAMICO__c&quot;,&quot;targetField&quot;:&quot;TAG_URL_DINAMICO__c&quot;},{&quot;sourceField&quot;:&quot;Data_INVIO__c&quot;,&quot;targetField&quot;:&quot;Data_INVIO__c&quot;},{&quot;sourceField&quot;:&quot;ContactKey__c&quot;,&quot;targetField&quot;:&quot;ContactKey__c&quot;},{&quot;sourceField&quot;:&quot;Nome_PIATTAFORMA__c&quot;,&quot;targetField&quot;:&quot;Nome_PIATTAFORMA__c&quot;},{&quot;sourceField&quot;:&quot;Timezone_IP__c&quot;,&quot;targetField&quot;:&quot;Timezone_IP__c&quot;},{&quot;sourceField&quot;:&quot;Nazione_IP__c&quot;,&quot;targetField&quot;:&quot;Nazione_IP__c&quot;},{&quot;sourceField&quot;:&quot;TAG_ID__c&quot;,&quot;targetField&quot;:&quot;TAG_ID__c&quot;},{&quot;sourceField&quot;:&quot;Descrizione_CLIENT__c&quot;,&quot;targetField&quot;:&quot;Descrizione_CLIENT__c&quot;},{&quot;sourceField&quot;:&quot;Indirizzo_IP__c&quot;,&quot;targetField&quot;:&quot;Indirizzo_IP__c&quot;},{&quot;sourceField&quot;:&quot;ID_COMUNICAZIONE__c&quot;,&quot;targetField&quot;:&quot;ID_COMUNICAZIONE__c&quot;},{&quot;sourceField&quot;:&quot;CANALE__c&quot;,&quot;targetField&quot;:&quot;CANALE__c&quot;},{&quot;sourceField&quot;:&quot;Nome_CLIENT__c&quot;,&quot;targetField&quot;:&quot;Nome_CLIENT__c&quot;},{&quot;sourceField&quot;:&quot;Regione_IP__c&quot;,&quot;targetField&quot;:&quot;Regione_IP__c&quot;},{&quot;sourceField&quot;:&quot;Categoria_EVENTO__c&quot;,&quot;targetField&quot;:&quot;Categoria_EVENTO__c&quot;},{&quot;sourceField&quot;:&quot;TAG_URL__c&quot;,&quot;targetField&quot;:&quot;TAG_URL__c&quot;},{&quot;sourceField&quot;:&quot;Tipo_PIATTAFORMA__c&quot;,&quot;targetField&quot;:&quot;Tipo_PIATTAFORMA__c&quot;},{&quot;sourceField&quot;:&quot;ListID__c&quot;,&quot;targetField&quot;:&quot;ListID__c&quot;},{&quot;sourceField&quot;:&quot;BatchID__c&quot;,&quot;targetField&quot;:&quot;BatchID__c&quot;},{&quot;sourceField&quot;:&quot;Data_EVENTO__c&quot;,&quot;targetField&quot;:&quot;Data_EVENTO__c&quot;},{&quot;sourceField&quot;:&quot;ID_SCRIBA__c&quot;,&quot;targetField&quot;:&quot;ID_SCRIBA__c&quot;},{&quot;sourceField&quot;:&quot;ISP__c&quot;,&quot;targetField&quot;:&quot;ISP__c&quot;},{&quot;sourceField&quot;:&quot;Data_EVENTO_dateformat__c&quot;,&quot;targetField&quot;:&quot;Data_EVENTO_dateformat__c&quot;},{&quot;sourceField&quot;:&quot;cdp_sys_PartitionDate__c&quot;,&quot;targetField&quot;:&quot;cdp_sys_PartitionDate__c&quot;},{&quot;sourceField&quot;:&quot;Data_APERTURA__c&quot;,&quot;targetField&quot;:&quot;Data_APERTURA__c&quot;},{&quot;sourceField&quot;:&quot;Tipo_CLIENT__c&quot;,&quot;targetField&quot;:&quot;Tipo_CLIENT__c&quot;},{&quot;sourceField&quot;:&quot;Tipo_EVENTO__c&quot;,&quot;targetField&quot;:&quot;Tipo_EVENTO__c&quot;},{&quot;sourceField&quot;:&quot;Dettagli_EVENTO__c&quot;,&quot;targetField&quot;:&quot;Dettagli_EVENTO__c&quot;},{&quot;sourceField&quot;:&quot;ID_TEMPLATE__c&quot;,&quot;targetField&quot;:&quot;ID_TEMPLATE__c&quot;},{&quot;sourceField&quot;:&quot;JobID__c&quot;,&quot;targetField&quot;:&quot;JobID__c&quot;},{&quot;sourceField&quot;:&quot;Citt_IP__c&quot;,&quot;targetField&quot;:&quot;Citt_IP__c&quot;},{&quot;sourceField&quot;:&quot;Descrizione_PIATTAFORMA__c&quot;,&quot;targetField&quot;:&quot;Descrizione_PIATTAFORMA__c&quot;},{&quot;sourceField&quot;:&quot;InternalOrganization__c&quot;,&quot;targetField&quot;:&quot;InternalOrganization__c&quot;},{&quot;sourceField&quot;:&quot;Data_CLICK__c&quot;,&quot;targetField&quot;:&quot;Data_CLICK__c&quot;},{&quot;sourceField&quot;:&quot;cdp_sys_SourceVersion__c&quot;,&quot;targetField&quot;:&quot;cdp_sys_SourceVersion__c&quot;},{&quot;sourceField&quot;:&quot;Transcodifica_MCE_CRMA_Test.DES_MACRO_EVENTO__c&quot;,&quot;targetField&quot;:&quot;DES_MACRO_EVENTO__c&quot;},{&quot;sourceField&quot;:&quot;Transcodifica_MCE_CRMA_Test.DES_EVENT_CLASS__c&quot;,&quot;targetField&quot;:&quot;DES_EVENT_CLASS__c&quot;},{&quot;sourceField&quot;:&quot;Transcodifica_MCE_CRMA_Test.EVENT_ID__c&quot;,&quot;targetField&quot;:&quot;EVENT_ID__c&quot;},{&quot;sourceField&quot;:&quot;Transcodifica_MCE_CRMA_Test.DES_CRMO_COD_TIPO__c&quot;,&quot;targetField&quot;:&quot;DES_CRMO_COD_TIPO__c&quot;},{&quot;sourceField&quot;:&quot;Transcodifica_MCE_CRMA_Test.DESC_EVENTO__c&quot;,&quot;targetField&quot;:&quot;DESC_EVENTO__c&quot;}],&quot;name&quot;:&quot;SCRIBA_DLOStorico_MCE__dll&quot;,&quot;writeMode&quot;:&quot;MERGE&quot;,&quot;type&quot;:&quot;dataLakeObject&quot;,&quot;priority&quot;:0}},&quot;OUTPUT1&quot;:{&quot;sources&quot;:[&quot;FORMULA4&quot;],&quot;action&quot;:&quot;outputD360&quot;,&quot;parameters&quot;:{&quot;fieldsMappings&quot;:[{&quot;sourceField&quot;:&quot;Data_INVIO__c&quot;,&quot;targetField&quot;:&quot;Data_INVIO__c&quot;},{&quot;sourceField&quot;:&quot;ContactKey__c&quot;,&quot;targetField&quot;:&quot;ContactKey__c&quot;},{&quot;sourceField&quot;:&quot;ID_COMUNICAZIONE__c&quot;,&quot;targetField&quot;:&quot;ID_COMUNICAZIONE__c&quot;},{&quot;sourceField&quot;:&quot;CANALE__c&quot;,&quot;targetField&quot;:&quot;CANALE__c&quot;},{&quot;sourceField&quot;:&quot;Data_EVENTO_dateformat__c&quot;,&quot;targetField&quot;:&quot;Data_EVENTO_dateformat__c&quot;},{&quot;sourceField&quot;:&quot;Transcodifica_MCE_CRMA_Test.DES_MACRO_EVENTO__c&quot;,&quot;targetField&quot;:&quot;DES_MACRO_EVENTO__c&quot;},{&quot;sourceField&quot;:&quot;Transcodifica_MCE_CRMA_Test.DES_EVENT_CLASS__c&quot;,&quot;targetField&quot;:&quot;DES_EVENT_CLASS__c&quot;},{&quot;sourceField&quot;:&quot;Transcodifica_MCE_CRMA_Test.DES_CRMO_COD_TIPO__c&quot;,&quot;targetField&quot;:&quot;DES_CRMO_COD_TIPO__c&quot;},{&quot;sourceField&quot;:&quot;Transcodifica_MCE_CRMA_Test.DESC_EVENTO__c&quot;,&quot;targetField&quot;:&quot;DESC_EVENTO__c&quot;},{&quot;sourceField&quot;:&quot;Max_Peso_Formula__c&quot;,&quot;targetField&quot;:&quot;Peso_DLO_Appoggio__c&quot;},{&quot;sourceField&quot;:&quot;Tipo_EVENTO_Formula__c&quot;,&quot;targetField&quot;:&quot;Tipo_EVENTO_DLO_Appoggio__c&quot;},{&quot;sourceField&quot;:&quot;Data_Inserimento_DLO&quot;,&quot;targetField&quot;:&quot;Data_Inserimento_DLO__c&quot;}],&quot;name&quot;:&quot;SCRIBA_Intermedio_MCE__dll&quot;,&quot;writeMode&quot;:&quot;MERGE&quot;,&quot;type&quot;:&quot;dataLakeObject&quot;,&quot;priority&quot;:0}},&quot;OUTPUT3&quot;:{&quot;sources&quot;:[&quot;FORMULA5&quot;],&quot;action&quot;:&quot;outputD360&quot;,&quot;parameters&quot;:{&quot;fieldsMappings&quot;:[{&quot;sourceField&quot;:&quot;CRMA_TRACKING_ID__c&quot;,&quot;targetField&quot;:&quot;CMRA_TRACKING_ID__c&quot;},{&quot;sourceField&quot;:&quot;DES_CRMO_COD_TIPO__c&quot;,&quot;targetField&quot;:&quot;DES_CRMO_COD_TIPO__c&quot;},{&quot;sourceField&quot;:&quot;CANALE_CONTATTO__c&quot;,&quot;targetField&quot;:&quot;CANALE__c&quot;},{&quot;sourceField&quot;:&quot;DATA_INVIO_COM__c&quot;,&quot;targetField&quot;:&quot;Data_INVIO__c&quot;},{&quot;sourceField&quot;:&quot;CF_PIVA__c&quot;,&quot;targetField&quot;:&quot;CF_PIVA__c&quot;},{&quot;sourceField&quot;:&quot;DES_MACRO_EVENTO__c&quot;,&quot;targetField&quot;:&quot;DES_MACRO_EVENTO__c&quot;},{&quot;sourceField&quot;:&quot;DESC_EVENTO__c&quot;,&quot;targetField&quot;:&quot;DESC_EVENTO__c&quot;},{&quot;sourceField&quot;:&quot;DES_EVENT_CLASS__c&quot;,&quot;targetField&quot;:&quot;DES_EVENT_CLASS__c&quot;},{&quot;sourceField&quot;:&quot;Max_Peso_Formula&quot;,&quot;targetField&quot;:&quot;PESO_Appoggio__c&quot;},{&quot;sourceField&quot;:&quot;COD_ESITO_Formula&quot;,&quot;targetField&quot;:&quot;COD_ESITO_Appoggio__c&quot;},{&quot;sourceField&quot;:&quot;Data_Inserimento_DLO&quot;,&quot;targetField&quot;:&quot;Data_Inserimento_DLO__c&quot;}],&quot;name&quot;:&quot;SCRIBA_DLOAppoggio_CRMA__dll&quot;,&quot;writeMode&quot;:&quot;MERGE&quot;,&quot;type&quot;:&quot;dataLakeObject&quot;,&quot;priority&quot;:0}},&quot;LOAD_DATASET5&quot;:{&quot;sources&quot;:[],&quot;action&quot;:&quot;load&quot;,&quot;parameters&quot;:{&quot;fields&quot;:[&quot;KQ_CMRA_TRACKING_ID__c&quot;,&quot;DataSource__c&quot;,&quot;cdp_sys_SourceVersion__c&quot;,&quot;InternalOrganization__c&quot;,&quot;CANALE__c&quot;,&quot;CMRA_TRACKING_ID__c&quot;,&quot;Data_INVIO__c&quot;,&quot;cdp_sys_PartitionDate__c&quot;,&quot;DES_MACRO_EVENTO__c&quot;,&quot;CF_PIVA__c&quot;,&quot;PESO_REGISTRO__c&quot;,&quot;DESC_EVENTO__c&quot;,&quot;COD_ESITO_Registro__c&quot;,&quot;DES_EVENT_CLASS__c&quot;,&quot;DataSourceObject__c&quot;,&quot;DES_CRMO_COD_TIPO__c&quot;],&quot;sampleDetails&quot;:{&quot;dataspace&quot;:&quot;default&quot;,&quot;type&quot;:&quot;TopN&quot;},&quot;dataset&quot;:{&quot;name&quot;:&quot;SCRIBA_Registro_CRMA__dll&quot;,&quot;type&quot;:&quot;dataLakeObject&quot;}}},&quot;LOAD_DATASET4&quot;:{&quot;sources&quot;:[],&quot;action&quot;:&quot;load&quot;,&quot;parameters&quot;:{&quot;fields&quot;:[&quot;KQ_ID_SCRIBA__c&quot;,&quot;DT_ESITO__c&quot;,&quot;CRMO_COD_TIPO__c&quot;,&quot;CRMA_TRACKING_ID__c&quot;,&quot;COD_MACRO_EVENTO__c&quot;,&quot;AGENZIA_FIGLIA__c&quot;,&quot;DES_CRMO_COD_TIPO__c&quot;,&quot;DATA_CARICAMENTO__c&quot;,&quot;CANALE_CONTATTO__c&quot;,&quot;DES_ESITO__c&quot;,&quot;COMPAGNIA__c&quot;,&quot;COD_ESITO__c&quot;,&quot;CONT_ID__c&quot;,&quot;DATA_INVIO_COM__c&quot;,&quot;cdp_sys_PartitionDate__c&quot;,&quot;CF_PIVA__c&quot;,&quot;ID_SCRIBA__c&quot;,&quot;AGENZIA_MADRE__c&quot;,&quot;RAMO_POLIZZA__c&quot;,&quot;DES_MACRO_EVENTO__c&quot;,&quot;DESC_EVENTO__c&quot;,&quot;COD_EVENT_CLASS__c&quot;,&quot;DATA_GENERAZIONE_CONTATTO__c&quot;,&quot;COD_EVENTO__c&quot;,&quot;DES_EVENT_CLASS__c&quot;,&quot;RECAPITO__c&quot;,&quot;DataSource__c&quot;,&quot;DataSourceObject__c&quot;,&quot;InternalOrganization__c&quot;,&quot;POLIZZA__c&quot;,&quot;SIST_DEST__c&quot;,&quot;cdp_sys_SourceVersion__c&quot;],&quot;sampleDetails&quot;:{&quot;dataspace&quot;:&quot;default&quot;,&quot;type&quot;:&quot;TopN&quot;},&quot;dataset&quot;:{&quot;name&quot;:&quot;SCRIBA_TracciatoTest_CRMA__dll&quot;,&quot;type&quot;:&quot;dataLakeObject&quot;}}},&quot;LOAD_DATASET3&quot;:{&quot;sources&quot;:[],&quot;action&quot;:&quot;load&quot;,&quot;parameters&quot;:{&quot;fields&quot;:[&quot;KQ_ID_COMUNICAZIONE__c&quot;,&quot;DES_EVENT_CLASS__c&quot;,&quot;DESC_EVENTO__c&quot;,&quot;Peso_Registro_Comunicazioni__c&quot;,&quot;DES_CRMO_COD_TIPO__c&quot;,&quot;DES_MACRO_EVENTO__c&quot;,&quot;ContactKey__c&quot;,&quot;Data_EVENTO_dateformat__c&quot;,&quot;cdp_sys_PartitionDate__c&quot;,&quot;ID_COMUNICAZIONE__c&quot;,&quot;DataSourceObject__c&quot;,&quot;Data_INVIO__c&quot;,&quot;Tipo_EVENTO_Registro_Comunicazioni__c&quot;,&quot;InternalOrganization__c&quot;,&quot;CANALE__c&quot;,&quot;DataSource__c&quot;,&quot;cdp_sys_SourceVersion__c&quot;],&quot;sampleDetails&quot;:{&quot;dataspace&quot;:&quot;default&quot;,&quot;type&quot;:&quot;TopN&quot;},&quot;dataset&quot;:{&quot;name&quot;:&quot;SCRIBA_Registro_MCE__dll&quot;,&quot;type&quot;:&quot;dataLakeObject&quot;}}},&quot;LOAD_DATASET2&quot;:{&quot;sources&quot;:[],&quot;action&quot;:&quot;load&quot;,&quot;parameters&quot;:{&quot;fields&quot;:[&quot;KQ_ST_TP_CD__c&quot;,&quot;DES_ST_TP_CD__c&quot;,&quot;PESO_livello_di_importanza__c&quot;,&quot;ST_TP_CD__c&quot;,&quot;InternalOrganization__c&quot;,&quot;DataSource__c&quot;,&quot;DataSourceObject__c&quot;,&quot;cdp_sys_SourceVersion__c&quot;],&quot;sampleDetails&quot;:{&quot;dataspace&quot;:&quot;default&quot;,&quot;type&quot;:&quot;TopN&quot;},&quot;dataset&quot;:{&quot;name&quot;:&quot;SCRIBA_Transcodifica_PesiEsiti_MCE__dll&quot;,&quot;type&quot;:&quot;dataLakeObject&quot;}}},&quot;LOAD_DATASET1&quot;:{&quot;sources&quot;:[],&quot;action&quot;:&quot;load&quot;,&quot;parameters&quot;:{&quot;fields&quot;:[&quot;KQ_PROVIDER_TEMPLATE__c&quot;,&quot;DUR_ARCH_DOC__c&quot;,&quot;COD_AREA__c&quot;,&quot;PROVIDER_TEMPLATE__c&quot;,&quot;DES_MACRO_EVENTO__c&quot;,&quot;InternalOrganization__c&quot;,&quot;DES_EVENT_CLASS__c&quot;,&quot;EVENT_ID__c&quot;,&quot;PRIVIDER__c&quot;,&quot;EVENT_TYPE__c&quot;,&quot;DataSourceObject__c&quot;,&quot;FLG_ARCH_DOC__c&quot;,&quot;CRMO_COD_TIPO__c&quot;,&quot;DES_CRMO_COD_TIPO__c&quot;,&quot;FLAG_CONS_SOST__c&quot;,&quot;FLG_VALIDAZIONE__c&quot;,&quot;COD_EVENT_CLASS__c&quot;,&quot;DataSource__c&quot;,&quot;DESC_EVENTO__c&quot;,&quot;FLG_PRES_ATT__c&quot;,&quot;CHANNEL_ID__c&quot;,&quot;COD_MACRO_EVENTO__c&quot;,&quot;DUR_CONST_SOST__c&quot;,&quot;cdp_sys_SourceVersion__c&quot;],&quot;sampleDetails&quot;:{&quot;dataspace&quot;:&quot;default&quot;,&quot;type&quot;:&quot;TopN&quot;},&quot;dataset&quot;:{&quot;name&quot;:&quot;Transcodifica_MCE_CRMA_Test__dll&quot;,&quot;type&quot;:&quot;dataLakeObject&quot;}}},&quot;LOAD_DATASET0&quot;:{&quot;sources&quot;:[],&quot;action&quot;:&quot;load&quot;,&quot;parameters&quot;:{&quot;fields&quot;:[&quot;KQ_ID_SCRIBA__c&quot;,&quot;TAG_URL_DINAMICO__c&quot;,&quot;Data_INVIO__c&quot;,&quot;ContactKey__c&quot;,&quot;Nome_PIATTAFORMA__c&quot;,&quot;Timezone_IP__c&quot;,&quot;Nazione_IP__c&quot;,&quot;TAG_ID__c&quot;,&quot;Descrizione_CLIENT__c&quot;,&quot;Indirizzo_IP__c&quot;,&quot;ID_COMUNICAZIONE__c&quot;,&quot;CANALE__c&quot;,&quot;Nome_CLIENT__c&quot;,&quot;Regione_IP__c&quot;,&quot;Categoria_EVENTO__c&quot;,&quot;TAG_URL__c&quot;,&quot;Tipo_PIATTAFORMA__c&quot;,&quot;ListID__c&quot;,&quot;BatchID__c&quot;,&quot;Data_EVENTO__c&quot;,&quot;ID_SCRIBA__c&quot;,&quot;ISP__c&quot;,&quot;Data_EVENTO_dateformat__c&quot;,&quot;cdp_sys_PartitionDate__c&quot;,&quot;Data_APERTURA__c&quot;,&quot;Tipo_CLIENT__c&quot;,&quot;Tipo_EVENTO__c&quot;,&quot;Dettagli_EVENTO__c&quot;,&quot;ID_TEMPLATE__c&quot;,&quot;JobID__c&quot;,&quot;Citt_IP__c&quot;,&quot;DataSourceObject__c&quot;,&quot;Descrizione_PIATTAFORMA__c&quot;,&quot;DataSource__c&quot;,&quot;InternalOrganization__c&quot;,&quot;Data_CLICK__c&quot;,&quot;cdp_sys_SourceVersion__c&quot;],&quot;sampleDetails&quot;:{&quot;dataspace&quot;:&quot;default&quot;,&quot;type&quot;:&quot;TopN&quot;},&quot;dataset&quot;:{&quot;name&quot;:&quot;SCRIBA_TracciatoTest_MCE__dll&quot;,&quot;type&quot;:&quot;dataLakeObject&quot;}}},&quot;FORMULA0&quot;:{&quot;sources&quot;:[&quot;FILL0&quot;],&quot;action&quot;:&quot;formula&quot;,&quot;parameters&quot;:{&quot;expressionType&quot;:&quot;SQL&quot;,&quot;fields&quot;:[{&quot;formulaExpression&quot;:&quot;case\r\n\twhen \&quot;SelectpesoMaggiore.Max_Peso\&quot; &gt;= \&quot;Complete_SCRIBA_Test_Registro.Peso_Registro_Comunicazioni__c\&quot;\r\n\tthen \&quot;SelectpesoMaggiore.Max_Peso\&quot;\r\n\telse\r\n\t\&quot;Complete_SCRIBA_Test_Registro.Peso_Registro_Comunicazioni__c\&quot;\r\nend&quot;,&quot;defaultValue&quot;:&quot;&quot;,&quot;precision&quot;:10,&quot;name&quot;:&quot;Max_Peso_Formula__c&quot;,&quot;scale&quot;:0,&quot;label&quot;:&quot;Max Peso Formula&quot;,&quot;businessType&quot;:&quot;NUMBER&quot;}]}},&quot;FORMULA1&quot;:{&quot;sources&quot;:[&quot;FORMULA0&quot;],&quot;action&quot;:&quot;formula&quot;,&quot;parameters&quot;:{&quot;expressionType&quot;:&quot;SQL&quot;,&quot;fields&quot;:[{&quot;formulaExpression&quot;:&quot;case\r\n\twhen \&quot;SelectpesoMaggiore.Max_Peso\&quot; &gt;= \&quot;Complete_SCRIBA_Test_Registro.Peso_Registro_Comunicazioni__c\&quot;\r\n\tthen \&quot;Tipo_EVENTO__c\&quot;\r\n\telse\r\n\t\&quot;Complete_SCRIBA_Test_Registro.Tipo_EVENTO_Registro_Comunicazioni__c\&quot;\r\nend&quot;,&quot;defaultValue&quot;:&quot;&quot;,&quot;precision&quot;:40,&quot;name&quot;:&quot;Tipo_EVENTO_Formula__c&quot;,&quot;label&quot;:&quot;Tipo_EVENTO Formula&quot;,&quot;businessType&quot;:&quot;TEXT&quot;}]}},&quot;FORMULA2&quot;:{&quot;sources&quot;:[&quot;FILL1&quot;],&quot;action&quot;:&quot;formula&quot;,&quot;parameters&quot;:{&quot;expressionType&quot;:&quot;SQL&quot;,&quot;fields&quot;:[{&quot;formulaExpression&quot;:&quot;case \r\n\twhen \&quot;SCRIBA_Transcodifica_PesiEsiti_MCE.PESO_livello_di_importanza__c\&quot; &gt;= \&quot;SCRIBA_Registro_CRMA.PESO_REGISTRO__c\&quot;\r\n\tthen \&quot;SCRIBA_Transcodifica_PesiEsiti_MCE.PESO_livello_di_importanza__c\&quot;\r\n\telse\r\n\t\&quot;SCRIBA_Registro_CRMA.PESO_REGISTRO__c\&quot;\r\nend&quot;,&quot;defaultValue&quot;:&quot;&quot;,&quot;precision&quot;:10,&quot;name&quot;:&quot;Max_Peso_Formula&quot;,&quot;scale&quot;:0,&quot;label&quot;:&quot;Max Peso Formula&quot;,&quot;businessType&quot;:&quot;NUMBER&quot;}]}},&quot;FORMULA3&quot;:{&quot;sources&quot;:[&quot;FORMULA2&quot;],&quot;action&quot;:&quot;formula&quot;,&quot;parameters&quot;:{&quot;expressionType&quot;:&quot;SQL&quot;,&quot;fields&quot;:[{&quot;formulaExpression&quot;:&quot;case \r\n\twhen \&quot;SCRIBA_Transcodifica_PesiEsiti_MCE.PESO_livello_di_importanza__c\&quot; &gt;= \&quot;SCRIBA_Registro_CRMA.PESO_REGISTRO__c\&quot;\r\n\tthen COD_ESITO__c\r\n\telse\r\n\t\&quot;SCRIBA_Registro_CRMA.COD_ESITO_Registro__c\&quot;\r\nend&quot;,&quot;defaultValue&quot;:&quot;&quot;,&quot;precision&quot;:40,&quot;name&quot;:&quot;COD_ESITO_Formula&quot;,&quot;label&quot;:&quot;COD_ESITO_Formula&quot;,&quot;businessType&quot;:&quot;TEXT&quot;}]}},&quot;FORMULA4&quot;:{&quot;sources&quot;:[&quot;FORMULA1&quot;],&quot;action&quot;:&quot;formula&quot;,&quot;parameters&quot;:{&quot;expressionType&quot;:&quot;SQL&quot;,&quot;fields&quot;:[{&quot;formulaExpression&quot;:&quot;now()&quot;,&quot;defaultValue&quot;:&quot;&quot;,&quot;name&quot;:&quot;Data_Inserimento_DLO&quot;,&quot;format&quot;:&quot;dd.MM.yy&quot;,&quot;label&quot;:&quot;Data Inserimento DLO&quot;,&quot;type&quot;:&quot;DATETIME&quot;}]}},&quot;FORMULA5&quot;:{&quot;sources&quot;:[&quot;FORMULA3&quot;],&quot;action&quot;:&quot;formula&quot;,&quot;parameters&quot;:{&quot;expressionType&quot;:&quot;SQL&quot;,&quot;fields&quot;:[{&quot;formulaExpression&quot;:&quot;now()&quot;,&quot;defaultValue&quot;:&quot;&quot;,&quot;name&quot;:&quot;Data_Inserimento_DLO&quot;,&quot;format&quot;:&quot;dd.MM.yyyy&quot;,&quot;label&quot;:&quot;Data Inserimento DLO&quot;,&quot;type&quot;:&quot;DATETIME&quot;}]}},&quot;EXTRACT0&quot;:{&quot;sources&quot;:[&quot;JOIN1&quot;],&quot;action&quot;:&quot;extractGrains&quot;,&quot;parameters&quot;:{&quot;grainExtractions&quot;:[]}},&quot;AGGREGATE1&quot;:{&quot;sources&quot;:[&quot;EXTRACT0&quot;],&quot;action&quot;:&quot;aggregate&quot;,&quot;parameters&quot;:{&quot;groupings&quot;:[&quot;ID_COMUNICAZIONE__c&quot;],&quot;nodeType&quot;:&quot;STANDARD&quot;,&quot;aggregations&quot;:[{&quot;name&quot;:&quot;Max_Peso&quot;,&quot;action&quot;:&quot;MAX&quot;,&quot;source&quot;:&quot;SCRIBA_Transcodifica_PesiEsiti_MCE.PESO_livello_di_importanza__c&quot;,&quot;label&quot;:&quot;Max Peso&quot;}]}},&quot;JOIN1&quot;:{&quot;schema&quot;:{&quot;slice&quot;:{&quot;mode&quot;:&quot;DROP&quot;,&quot;ignoreMissingFields&quot;:true,&quot;fields&quot;:[&quot;BatchID__c&quot;,&quot;Categoria_EVENTO__c&quot;,&quot;Citt_IP__c&quot;,&quot;DataSourceObject__c&quot;,&quot;DataSource__c&quot;,&quot;Data_APERTURA__c&quot;,&quot;Data_CLICK__c&quot;,&quot;Data_EVENTO__c&quot;,&quot;Descrizione_CLIENT__c&quot;,&quot;Descrizione_PIATTAFORMA__c&quot;,&quot;Dettagli_EVENTO__c&quot;,&quot;ID_SCRIBA__c&quot;,&quot;ID_TEMPLATE__c&quot;,&quot;ISP__c&quot;,&quot;Indirizzo_IP__c&quot;,&quot;InternalOrganization__c&quot;,&quot;JobID__c&quot;,&quot;KQ_ID_SCRIBA__c&quot;,&quot;ListID__c&quot;,&quot;Nazione_IP__c&quot;,&quot;Nome_CLIENT__c&quot;,&quot;Nome_PIATTAFORMA__c&quot;,&quot;Regione_IP__c&quot;,&quot;SCRIBA_Transcodifica_PesiEsiti_MCE.DES_ST_TP_CD__c&quot;,&quot;SCRIBA_Transcodifica_PesiEsiti_MCE.DataSourceObject__c&quot;,&quot;SCRIBA_Transcodifica_PesiEsiti_MCE.DataSource__c&quot;,&quot;SCRIBA_Transcodifica_PesiEsiti_MCE.InternalOrganization__c&quot;,&quot;SCRIBA_Transcodifica_PesiEsiti_MCE.KQ_ST_TP_CD__c&quot;,&quot;SCRIBA_Transcodifica_PesiEsiti_MCE.ST_TP_CD__c&quot;,&quot;SCRIBA_Transcodifica_PesiEsiti_MCE.cdp_sys_SourceVersion__c&quot;,&quot;TAG_ID__c&quot;,&quot;TAG_URL_DINAMICO__c&quot;,&quot;TAG_URL__c&quot;,&quot;Timezone_IP__c&quot;,&quot;Tipo_CLIENT__c&quot;,&quot;Tipo_PIATTAFORMA__c&quot;,&quot;Transcodifica_MCE_CRMA_Test.EVENT_ID__c&quot;,&quot;cdp_sys_PartitionDate__c&quot;,&quot;cdp_sys_SourceVersion__c&quot;]}},&quot;sources&quot;:[&quot;JOIN0&quot;,&quot;LOAD_DATASET2&quot;],&quot;action&quot;:&quot;join&quot;,&quot;parameters&quot;:{&quot;leftKeys&quot;:[&quot;Tipo_EVENTO__c&quot;],&quot;rightQualifier&quot;:&quot;SCRIBA_Transcodifica_PesiEsiti_MCE&quot;,&quot;rightKeys&quot;:[&quot;ST_TP_CD__c&quot;],&quot;joinType&quot;:&quot;LEFT_OUTER&quot;}},&quot;JOIN0&quot;:{&quot;schema&quot;:{&quot;slice&quot;:{&quot;mode&quot;:&quot;DROP&quot;,&quot;ignoreMissingFields&quot;:true,&quot;fields&quot;:[&quot;Transcodifica_MCE_CRMA_Test.CHANNEL_ID__c&quot;,&quot;Transcodifica_MCE_CRMA_Test.COD_AREA__c&quot;,&quot;Transcodifica_MCE_CRMA_Test.COD_EVENT_CLASS__c&quot;,&quot;Transcodifica_MCE_CRMA_Test.COD_MACRO_EVENTO__c&quot;,&quot;Transcodifica_MCE_CRMA_Test.CRMO_COD_TIPO__c&quot;,&quot;Transcodifica_MCE_CRMA_Test.DUR_ARCH_DOC__c&quot;,&quot;Transcodifica_MCE_CRMA_Test.DUR_CONST_SOST__c&quot;,&quot;Transcodifica_MCE_CRMA_Test.DataSourceObject__c&quot;,&quot;Transcodifica_MCE_CRMA_Test.DataSource__c&quot;,&quot;Transcodifica_MCE_CRMA_Test.EVENT_TYPE__c&quot;,&quot;Transcodifica_MCE_CRMA_Test.FLAG_CONS_SOST__c&quot;,&quot;Transcodifica_MCE_CRMA_Test.FLG_ARCH_DOC__c&quot;,&quot;Transcodifica_MCE_CRMA_Test.FLG_PRES_ATT__c&quot;,&quot;Transcodifica_MCE_CRMA_Test.FLG_VALIDAZIONE__c&quot;,&quot;Transcodifica_MCE_CRMA_Test.InternalOrganization__c&quot;,&quot;Transcodifica_MCE_CRMA_Test.KQ_PROVIDER_TEMPLATE__c&quot;,&quot;Transcodifica_MCE_CRMA_Test.PRIVIDER__c&quot;,&quot;Transcodifica_MCE_CRMA_Test.PROVIDER_TEMPLATE__c&quot;,&quot;Transcodifica_MCE_CRMA_Test.cdp_sys_SourceVersion__c&quot;]}},&quot;sources&quot;:[&quot;LOAD_DATASET0&quot;,&quot;LOAD_DATASET1&quot;],&quot;action&quot;:&quot;join&quot;,&quot;parameters&quot;:{&quot;leftKeys&quot;:[&quot;ID_TEMPLATE__c&quot;],&quot;rightQualifier&quot;:&quot;Transcodifica_MCE_CRMA_Test&quot;,&quot;rightKeys&quot;:[&quot;PROVIDER_TEMPLATE__c&quot;],&quot;joinType&quot;:&quot;LEFT_OUTER&quot;}},&quot;FILL0&quot;:{&quot;schema&quot;:{&quot;slice&quot;:{&quot;mode&quot;:&quot;DROP&quot;,&quot;ignoreMissingFields&quot;:true,&quot;fields&quot;:[&quot;Complete_SCRIBA_Test_Registro.Peso_Registro_Comunicazioni__c&quot;]},&quot;fields&quot;:[{&quot;newProperties&quot;:{&quot;name&quot;:&quot;Complete_SCRIBA_Test_Registro.Peso_Registro_Comunicazioni__c&quot;},&quot;name&quot;:&quot;Com_Peso_Registro_Comunicazioni_fill__c&quot;}]},&quot;sources&quot;:[&quot;JOIN3&quot;],&quot;action&quot;:&quot;formula&quot;,&quot;parameters&quot;:{&quot;expressionType&quot;:&quot;SQL&quot;,&quot;fields&quot;:[{&quot;formulaExpression&quot;:&quot;coalesce(\&quot;Complete_SCRIBA_Test_Registro.Peso_Registro_Comunicazioni__c\&quot;, null, 0)&quot;,&quot;precision&quot;:18,&quot;name&quot;:&quot;Com_Peso_Registro_Comunicazioni_fill__c&quot;,&quot;scale&quot;:0,&quot;label&quot;:&quot;Peso Registro Comunicazioni&quot;,&quot;type&quot;:&quot;NUMBER&quot;}]}},&quot;FILL1&quot;:{&quot;schema&quot;:{&quot;slice&quot;:{&quot;mode&quot;:&quot;DROP&quot;,&quot;ignoreMissingFields&quot;:true,&quot;fields&quot;:[&quot;SCRIBA_Registro_CRMA.PESO_REGISTRO__c&quot;]},&quot;fields&quot;:[{&quot;newProperties&quot;:{&quot;name&quot;:&quot;SCRIBA_Registro_CRMA.PESO_REGISTRO__c&quot;},&quot;name&quot;:&quot;SCR_PESO_REGISTRO_fill__c&quot;}]},&quot;sources&quot;:[&quot;JOIN5&quot;],&quot;action&quot;:&quot;formula&quot;,&quot;parameters&quot;:{&quot;expressionType&quot;:&quot;SQL&quot;,&quot;fields&quot;:[{&quot;formulaExpression&quot;:&quot;coalesce(\&quot;SCRIBA_Registro_CRMA.PESO_REGISTRO__c\&quot;, null, 0)&quot;,&quot;precision&quot;:18,&quot;name&quot;:&quot;SCR_PESO_REGISTRO_fill__c&quot;,&quot;scale&quot;:0,&quot;label&quot;:&quot;PESO_REGISTRO&quot;,&quot;type&quot;:&quot;NUMBER&quot;}]}},&quot;JOIN5&quot;:{&quot;schema&quot;:{&quot;slice&quot;:{&quot;mode&quot;:&quot;DROP&quot;,&quot;ignoreMissingFields&quot;:true,&quot;fields&quot;:[&quot;SCRIBA_Registro_CRMA.KQ_CMRA_TRACKING_ID__c&quot;,&quot;SCRIBA_Registro_CRMA.DataSource__c&quot;,&quot;SCRIBA_Registro_CRMA.cdp_sys_SourceVersion__c&quot;,&quot;SCRIBA_Registro_CRMA.InternalOrganization__c&quot;,&quot;SCRIBA_Registro_CRMA.CANALE__c&quot;,&quot;SCRIBA_Registro_CRMA.CMRA_TRACKING_ID__c&quot;,&quot;SCRIBA_Registro_CRMA.Data_INVIO__c&quot;,&quot;SCRIBA_Registro_CRMA.cdp_sys_PartitionDate__c&quot;,&quot;SCRIBA_Registro_CRMA.DES_MACRO_EVENTO__c&quot;,&quot;SCRIBA_Registro_CRMA.CF_PIVA__c&quot;,&quot;SCRIBA_Registro_CRMA.DESC_EVENTO__c&quot;,&quot;SCRIBA_Registro_CRMA.DES_EVENT_CLASS__c&quot;,&quot;SCRIBA_Registro_CRMA.DataSourceObject__c&quot;,&quot;SCRIBA_Registro_CRMA.DES_CRMO_COD_TIPO__c&quot;]}},&quot;sources&quot;:[&quot;JOIN4&quot;,&quot;LOAD_DATASET5&quot;],&quot;action&quot;:&quot;join&quot;,&quot;parameters&quot;:{&quot;leftKeys&quot;:[&quot;CRMA_TRACKING_ID__c&quot;],&quot;rightQualifier&quot;:&quot;SCRIBA_Registro_CRMA&quot;,&quot;rightKeys&quot;:[&quot;CMRA_TRACKING_ID__c&quot;],&quot;joinType&quot;:&quot;LEFT_OUTER&quot;}},&quot;JOIN4&quot;:{&quot;schema&quot;:{&quot;slice&quot;:{&quot;mode&quot;:&quot;DROP&quot;,&quot;ignoreMissingFields&quot;:true,&quot;fields&quot;:[&quot;DT_ESITO__c&quot;,&quot;CRMO_COD_TIPO__c&quot;,&quot;COD_MACRO_EVENTO__c&quot;,&quot;AGENZIA_FIGLIA__c&quot;,&quot;DATA_CARICAMENTO__c&quot;,&quot;DES_ESITO__c&quot;,&quot;CONT_ID__c&quot;,&quot;cdp_sys_PartitionDate__c&quot;,&quot;ID_SCRIBA__c&quot;,&quot;AGENZIA_MADRE__c&quot;,&quot;RAMO_POLIZZA__c&quot;,&quot;COD_EVENT_CLASS__c&quot;,&quot;DATA_GENERAZIONE_CONTATTO__c&quot;,&quot;COD_EVENTO__c&quot;,&quot;RECAPITO__c&quot;,&quot;DataSource__c&quot;,&quot;DataSourceObject__c&quot;,&quot;InternalOrganization__c&quot;,&quot;POLIZZA__c&quot;,&quot;SIST_DEST__c&quot;,&quot;cdp_sys_SourceVersion__c&quot;,&quot;SCRIBA_Transcodifica_PesiEsiti_MCE.KQ_ST_TP_CD__c&quot;,&quot;SCRIBA_Transcodifica_PesiEsiti_MCE.DES_ST_TP_CD__c&quot;,&quot;SCRIBA_Transcodifica_PesiEsiti_MCE.ST_TP_CD__c&quot;,&quot;SCRIBA_Transcodifica_PesiEsiti_MCE.InternalOrganization__c&quot;,&quot;SCRIBA_Transcodifica_PesiEsiti_MCE.DataSource__c&quot;,&quot;SCRIBA_Transcodifica_PesiEsiti_MCE.DataSourceObject__c&quot;,&quot;SCRIBA_Transcodifica_PesiEsiti_MCE.cdp_sys_SourceVersion__c&quot;]}},&quot;sources&quot;:[&quot;LOAD_DATASET4&quot;,&quot;LOAD_DATASET2&quot;],&quot;action&quot;:&quot;join&quot;,&quot;parameters&quot;:{&quot;leftKeys&quot;:[&quot;COD_ESITO__c&quot;],&quot;rightQualifier&quot;:&quot;SCRIBA_Transcodifica_PesiEsiti_MCE&quot;,&quot;rightKeys&quot;:[&quot;ST_TP_CD__c&quot;],&quot;joinType&quot;:&quot;LEFT_OUTER&quot;}},&quot;JOIN3&quot;:{&quot;schema&quot;:{&quot;slice&quot;:{&quot;mode&quot;:&quot;DROP&quot;,&quot;ignoreMissingFields&quot;:true,&quot;fields&quot;:[&quot;SCRIBA_Transcodifica_PesiEsiti_MCE.PESO_livello_di_importanza__c&quot;,&quot;Complete_SCRIBA_Test_Registro.KQ_ID_COMUNICAZIONE__c&quot;,&quot;Complete_SCRIBA_Test_Registro.Data_INVIO__c&quot;,&quot;Complete_SCRIBA_Test_Registro.cdp_sys_SourceVersion__c&quot;,&quot;Complete_SCRIBA_Test_Registro.ContactKey__c&quot;,&quot;Complete_SCRIBA_Test_Registro.DES_EVENT_CLASS__c&quot;,&quot;Complete_SCRIBA_Test_Registro.DES_MACRO_EVENTO__c&quot;,&quot;Complete_SCRIBA_Test_Registro.DESC_EVENTO__c&quot;,&quot;Complete_SCRIBA_Test_Registro.InternalOrganization__c&quot;,&quot;Complete_SCRIBA_Test_Registro.DES_CRMO_COD_TIPO__c&quot;,&quot;Complete_SCRIBA_Test_Registro.ID_COMUNICAZIONE__c&quot;,&quot;Complete_SCRIBA_Test_Registro.DataSourceObject__c&quot;,&quot;Complete_SCRIBA_Test_Registro.Data_EVENTO_dateformat__c&quot;,&quot;Complete_SCRIBA_Test_Registro.cdp_sys_PartitionDate__c&quot;,&quot;Complete_SCRIBA_Test_Registro.CANALE__c&quot;,&quot;Complete_SCRIBA_Test_Registro.DataSource__c&quot;]}},&quot;sources&quot;:[&quot;JOIN2&quot;,&quot;LOAD_DATASET3&quot;],&quot;action&quot;:&quot;join&quot;,&quot;parameters&quot;:{&quot;leftKeys&quot;:[&quot;ID_COMUNICAZIONE__c&quot;],&quot;rightQualifier&quot;:&quot;Complete_SCRIBA_Test_Registro&quot;,&quot;rightKeys&quot;:[&quot;ID_COMUNICAZIONE__c&quot;],&quot;joinType&quot;:&quot;LEFT_OUTER&quot;}},&quot;JOIN2&quot;:{&quot;schema&quot;:{&quot;slice&quot;:{&quot;mode&quot;:&quot;DROP&quot;,&quot;ignoreMissingFields&quot;:true,&quot;fields&quot;:[&quot;ID_SCRIBA__c&quot;,&quot;SelectpesoMaggiore.ID_COMUNICAZIONE__c&quot;]}},&quot;sources&quot;:[&quot;JOIN1&quot;,&quot;AGGREGATE1&quot;],&quot;action&quot;:&quot;join&quot;,&quot;parameters&quot;:{&quot;leftKeys&quot;:[&quot;ID_COMUNICAZIONE__c&quot;,&quot;SCRIBA_Transcodifica_PesiEsiti_MCE.PESO_livello_di_importanza__c&quot;],&quot;rightQualifier&quot;:&quot;SelectpesoMaggiore&quot;,&quot;rightKeys&quot;:[&quot;ID_COMUNICAZIONE__c&quot;,&quot;Max_Peso&quot;],&quot;joinType&quot;:&quot;INNER&quot;}}},&quot;ui&quot;:{&quot;hiddenColumns&quot;:[],&quot;connectors&quot;:[{&quot;source&quot;:&quot;LOAD_DATASET0&quot;,&quot;target&quot;:&quot;JOIN0&quot;},{&quot;source&quot;:&quot;LOAD_DATASET1&quot;,&quot;target&quot;:&quot;JOIN0&quot;},{&quot;source&quot;:&quot;JOIN0&quot;,&quot;target&quot;:&quot;JOIN1&quot;},{&quot;source&quot;:&quot;LOAD_DATASET2&quot;,&quot;target&quot;:&quot;JOIN1&quot;},{&quot;source&quot;:&quot;JOIN1&quot;,&quot;target&quot;:&quot;AGGREGATE0&quot;},{&quot;source&quot;:&quot;JOIN1&quot;,&quot;target&quot;:&quot;JOIN2&quot;},{&quot;source&quot;:&quot;AGGREGATE0&quot;,&quot;target&quot;:&quot;JOIN2&quot;},{&quot;source&quot;:&quot;JOIN2&quot;,&quot;target&quot;:&quot;JOIN3&quot;},{&quot;source&quot;:&quot;JOIN3&quot;,&quot;target&quot;:&quot;TRANSFORM0&quot;},{&quot;source&quot;:&quot;TRANSFORM0&quot;,&quot;target&quot;:&quot;OUTPUT1&quot;},{&quot;source&quot;:&quot;JOIN0&quot;,&quot;target&quot;:&quot;OUTPUT2&quot;},{&quot;source&quot;:&quot;LOAD_DATASET3&quot;,&quot;target&quot;:&quot;JOIN3&quot;},{&quot;source&quot;:&quot;LOAD_DATASET4&quot;,&quot;target&quot;:&quot;JOIN4&quot;},{&quot;source&quot;:&quot;LOAD_DATASET2&quot;,&quot;target&quot;:&quot;JOIN4&quot;},{&quot;source&quot;:&quot;JOIN4&quot;,&quot;target&quot;:&quot;JOIN5&quot;},{&quot;source&quot;:&quot;LOAD_DATASET5&quot;,&quot;target&quot;:&quot;JOIN5&quot;},{&quot;source&quot;:&quot;JOIN5&quot;,&quot;target&quot;:&quot;TRANSFORM1&quot;},{&quot;source&quot;:&quot;TRANSFORM1&quot;,&quot;target&quot;:&quot;OUTPUT3&quot;}],&quot;nodes&quot;:{&quot;AGGREGATE0&quot;:{&quot;connectors&quot;:[{&quot;source&quot;:&quot;EXTRACT0&quot;,&quot;target&quot;:&quot;AGGREGATE1&quot;}],&quot;top&quot;:112,&quot;left&quot;:532,&quot;description&quot;:&quot;&quot;,&quot;label&quot;:&quot;Select peso Maggiore&quot;,&quot;type&quot;:&quot;AGGREGATE&quot;,&quot;graph&quot;:{}},&quot;OUTPUT2&quot;:{&quot;top&quot;:252,&quot;left&quot;:392,&quot;description&quot;:&quot;&quot;,&quot;label&quot;:&quot;Storico MCE&quot;,&quot;type&quot;:&quot;OUTPUT&quot;},&quot;OUTPUT1&quot;:{&quot;top&quot;:112,&quot;left&quot;:1092,&quot;description&quot;:&quot;&quot;,&quot;label&quot;:&quot;DLO Appoggio MCE&quot;,&quot;type&quot;:&quot;OUTPUT&quot;},&quot;OUTPUT3&quot;:{&quot;top&quot;:392.2,&quot;left&quot;:1092.1,&quot;description&quot;:&quot;&quot;,&quot;label&quot;:&quot;DLO Appoggio CRMA&quot;,&quot;type&quot;:&quot;OUTPUT&quot;},&quot;JOIN1&quot;:{&quot;top&quot;:112,&quot;left&quot;:392,&quot;description&quot;:&quot;&quot;,&quot;label&quot;:&quot;Recupero Peso Numerico MCE&quot;,&quot;type&quot;:&quot;JOIN&quot;},&quot;JOIN0&quot;:{&quot;top&quot;:112,&quot;left&quot;:252,&quot;description&quot;:&quot;&quot;,&quot;label&quot;:&quot;Transcodifica MCE-CRMA&quot;,&quot;type&quot;:&quot;JOIN&quot;},&quot;LOAD_DATASET5&quot;:{&quot;top&quot;:531.9,&quot;left&quot;:672.1,&quot;label&quot;:&quot;SCRIBA_Registro_CRMA&quot;,&quot;type&quot;:&quot;LOAD_DATASET&quot;,&quot;parameters&quot;:{&quot;sampleSize&quot;:500}},&quot;LOAD_DATASET4&quot;:{&quot;top&quot;:392.3,&quot;left&quot;:112,&quot;label&quot;:&quot;SCRIBA_TracciatoTest_CRMA&quot;,&quot;type&quot;:&quot;LOAD_DATASET&quot;,&quot;parameters&quot;:{&quot;sampleSize&quot;:500}},&quot;LOAD_DATASET3&quot;:{&quot;top&quot;:252,&quot;left&quot;:672,&quot;label&quot;:&quot;SCRIBA_Registro_MCE&quot;,&quot;type&quot;:&quot;LOAD_DATASET&quot;,&quot;parameters&quot;:{&quot;sampleSize&quot;:500}},&quot;LOAD_DATASET2&quot;:{&quot;top&quot;:252,&quot;left&quot;:252,&quot;label&quot;:&quot;SCRIBA_Transcodifica_PesiEsiti_MCE&quot;,&quot;type&quot;:&quot;LOAD_DATASET&quot;,&quot;parameters&quot;:{&quot;sampleSize&quot;:500}},&quot;LOAD_DATASET1&quot;:{&quot;top&quot;:252,&quot;left&quot;:112,&quot;label&quot;:&quot;Transcodifica_MCE_CRMA_Test&quot;,&quot;type&quot;:&quot;LOAD_DATASET&quot;,&quot;parameters&quot;:{&quot;sampleSize&quot;:500}},&quot;LOAD_DATASET0&quot;:{&quot;top&quot;:112,&quot;left&quot;:112,&quot;label&quot;:&quot;SCRIBA_TracciatoTest_MCE&quot;,&quot;type&quot;:&quot;LOAD_DATASET&quot;,&quot;parameters&quot;:{&quot;sampleSize&quot;:500}},&quot;TRANSFORM0&quot;:{&quot;connectors&quot;:[{&quot;source&quot;:&quot;FILL0&quot;,&quot;target&quot;:&quot;FORMULA0&quot;},{&quot;source&quot;:&quot;FORMULA0&quot;,&quot;target&quot;:&quot;FORMULA1&quot;},{&quot;source&quot;:&quot;FORMULA1&quot;,&quot;target&quot;:&quot;FORMULA4&quot;}],&quot;top&quot;:112,&quot;left&quot;:952,&quot;description&quot;:&quot;&quot;,&quot;label&quot;:&quot;Select Peso Maggiore MCE&quot;,&quot;type&quot;:&quot;TRANSFORM&quot;,&quot;graph&quot;:{&quot;FILL0&quot;:{&quot;label&quot;:&quot;Fill&quot;,&quot;parameters&quot;:{&quot;type&quot;:&quot;FILL_UI&quot;}},&quot;FORMULA0&quot;:{&quot;label&quot;:&quot;Formula&quot;,&quot;parameters&quot;:{&quot;type&quot;:&quot;BASE_FORMULA_UI&quot;}},&quot;FORMULA1&quot;:{&quot;label&quot;:&quot;Formula&quot;,&quot;parameters&quot;:{&quot;type&quot;:&quot;BASE_FORMULA_UI&quot;}},&quot;FORMULA4&quot;:{&quot;label&quot;:&quot;Formula Now&quot;,&quot;parameters&quot;:{&quot;type&quot;:&quot;DATE_NOW_UI&quot;}}}},&quot;JOIN5&quot;:{&quot;top&quot;:392.2,&quot;left&quot;:811.9,&quot;description&quot;:&quot;&quot;,&quot;label&quot;:&quot;Match Registro CRMA&quot;,&quot;type&quot;:&quot;JOIN&quot;},&quot;JOIN4&quot;:{&quot;top&quot;:392,&quot;left&quot;:392,&quot;description&quot;:&quot;&quot;,&quot;label&quot;:&quot;Recupero Peso Numerico CRMA&quot;,&quot;type&quot;:&quot;JOIN&quot;},&quot;TRANSFORM1&quot;:{&quot;connectors&quot;:[{&quot;source&quot;:&quot;FILL1&quot;,&quot;target&quot;:&quot;FORMULA2&quot;},{&quot;source&quot;:&quot;FORMULA2&quot;,&quot;target&quot;:&quot;FORMULA3&quot;},{&quot;source&quot;:&quot;FORMULA3&quot;,&quot;target&quot;:&quot;FORMULA5&quot;}],&quot;top&quot;:392,&quot;left&quot;:952.2,&quot;description&quot;:&quot;&quot;,&quot;label&quot;:&quot;Select Peso Maggiore CRMA&quot;,&quot;type&quot;:&quot;TRANSFORM&quot;,&quot;graph&quot;:{&quot;FILL1&quot;:{&quot;label&quot;:&quot;Fill&quot;,&quot;parameters&quot;:{&quot;type&quot;:&quot;FILL_UI&quot;}},&quot;FORMULA2&quot;:{&quot;label&quot;:&quot;Formula&quot;,&quot;parameters&quot;:{&quot;type&quot;:&quot;BASE_FORMULA_UI&quot;}},&quot;FORMULA3&quot;:{&quot;label&quot;:&quot;Formula&quot;,&quot;parameters&quot;:{&quot;type&quot;:&quot;BASE_FORMULA_UI&quot;}},&quot;FORMULA5&quot;:{&quot;label&quot;:&quot;Formula Now&quot;,&quot;parameters&quot;:{&quot;type&quot;:&quot;DATE_NOW_UI&quot;}}}},&quot;JOIN3&quot;:{&quot;top&quot;:112,&quot;left&quot;:812,&quot;description&quot;:&quot;&quot;,&quot;label&quot;:&quot;Match Registro&quot;,&quot;type&quot;:&quot;JOIN&quot;},&quot;JOIN2&quot;:{&quot;top&quot;:112,&quot;left&quot;:672,&quot;description&quot;:&quot;&quot;,&quot;label&quot;:&quot;Recupero Info Mancanti&quot;,&quot;type&quot;:&quot;JOIN&quot;}}},&quot;tokenMap&quot;:{},&quot;dloTokenMap&quot;:{&quot;SCRIBA_DLOStorico_MCE__dll&quot;:&quot;SCRIBA_DLOStorico_MCE__dll&quot;,&quot;SCRIBA_Transcodifica_PesiEsiti_MCE__dll&quot;:&quot;SCRIBA_Transcodifica_PesiEsiti_MCE__dll&quot;,&quot;SCRIBA_DLOAppoggio_CRMA__dll&quot;:&quot;SCRIBA_DLOAppoggio_CRMA__dll&quot;,&quot;SCRIBA_TracciatoTest_MCE__dll&quot;:&quot;SCRIBA_TracciatoTest_MCE__dll&quot;,&quot;SCRIBA_TracciatoTest_CRMA__dll&quot;:&quot;SCRIBA_TracciatoTest_CRMA__dll&quot;,&quot;SCRIBA_Registro_CRMA__dll&quot;:&quot;SCRIBA_Registro_CRMA__dll&quot;,&quot;SCRIBA_Intermedio_MCE__dll&quot;:&quot;SCRIBA_Intermedio_MCE__dll&quot;,&quot;SCRIBA_Registro_MCE__dll&quot;:&quot;SCRIBA_Registro_MCE__dll&quot;,&quot;Transcodifica_MCE_CRMA_Test__dll&quot;:&quot;Transcodifica_MCE_CRMA_Test__dll&quot;},&quot;Output&quot;:[{&quot;name&quot;:&quot;SCRIBA_Intermedio_MCE__dll&quot;,&quot;label&quot;:&quot;SCRIBA_Intermedio_MCE&quot;,&quot;eventDateTimeFieldName&quot;:&quot;Data_EVENTO_dateformat&quot;,&quot;type&quot;:&quot;DataLakeObject&quot;,&quot;category&quot;:&quot;Engagement&quot;,&quot;fields&quot;:[{&quot;name&quot;:&quot;DES_CRMO_COD_TIPO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;DES_CRMO_COD_TIPO&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_DES_CRMO_COD_TIPO&quot;},{&quot;name&quot;:&quot;CANALE__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;CANALE&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_CANALE&quot;},{&quot;name&quot;:&quot;DES_MACRO_EVENTO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;DES_MACRO_EVENTO&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_DES_MACRO_EVENTO&quot;},{&quot;name&quot;:&quot;DES_EVENT_CLASS__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;DES_EVENT_CLASS&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_DES_EVENT_CLASS&quot;},{&quot;name&quot;:&quot;ID_COMUNICAZIONE__c&quot;,&quot;isPrimaryKey&quot;:true,&quot;label&quot;:&quot;ID_COMUNICAZIONE&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_ID_COMUNICAZIONE&quot;},{&quot;name&quot;:&quot;Data_EVENTO_dateformat__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Data_EVENTO_dateformat&quot;,&quot;type&quot;:&quot;DateTime&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Data_EVENTO_dateformat&quot;},{&quot;name&quot;:&quot;Data_INVIO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Data_INVIO&quot;,&quot;type&quot;:&quot;DateTime&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Data_INVIO&quot;},{&quot;name&quot;:&quot;Peso_DLO_Appoggio__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Peso DLO Appoggio&quot;,&quot;type&quot;:&quot;Number&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Peso_DLO_Appoggio&quot;},{&quot;name&quot;:&quot;DESC_EVENTO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;DESC_EVENTO&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_DESC_EVENTO&quot;},{&quot;name&quot;:&quot;Tipo_EVENTO_DLO_Appoggio__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Tipo_EVENTO DLO Appoggio&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Tipo_EVENTO_DLO_Appoggio&quot;},{&quot;name&quot;:&quot;ContactKey__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;ContactKey&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_ContactKey&quot;},{&quot;name&quot;:&quot;Data_Inserimento_DLO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Data Inserimento DLO&quot;,&quot;type&quot;:&quot;DateTime&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Data_Inserimento_DLO&quot;}]},{&quot;name&quot;:&quot;SCRIBA_DLOAppoggio_CRMA__dll&quot;,&quot;label&quot;:&quot;SCRIBA_DLOAppoggio_CRMA&quot;,&quot;eventDateTimeFieldName&quot;:&quot;Data_INVIO&quot;,&quot;type&quot;:&quot;DataLakeObject&quot;,&quot;category&quot;:&quot;Engagement&quot;,&quot;fields&quot;:[{&quot;name&quot;:&quot;COD_ESITO_Appoggio__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;COD_ESITO_Appoggio&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_COD_ESITO_Appoggio&quot;},{&quot;name&quot;:&quot;DESC_EVENTO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;DESC_EVENTO&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_DESC_EVENTO&quot;},{&quot;name&quot;:&quot;CMRA_TRACKING_ID__c&quot;,&quot;isPrimaryKey&quot;:true,&quot;label&quot;:&quot;CMRA_TRACKING_ID&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_CMRA_TRACKING_ID&quot;},{&quot;name&quot;:&quot;CF_PIVA__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;CF_PIVA&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_CF_PIVA&quot;},{&quot;name&quot;:&quot;DES_CRMO_COD_TIPO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;DES_CRMO_COD_TIPO&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_DES_CRMO_COD_TIPO&quot;},{&quot;name&quot;:&quot;CANALE__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;CANALE&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_CANALE&quot;},{&quot;name&quot;:&quot;PESO_Appoggio__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;PESO_Appoggio&quot;,&quot;type&quot;:&quot;Number&quot;,&quot;keyQualifierField&quot;:&quot;KQ_PESO_Appoggio&quot;},{&quot;name&quot;:&quot;DES_MACRO_EVENTO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;DES_MACRO_EVENTO&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_DES_MACRO_EVENTO&quot;},{&quot;name&quot;:&quot;DES_EVENT_CLASS__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;DES_EVENT_CLASS&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_DES_EVENT_CLASS&quot;},{&quot;name&quot;:&quot;Data_INVIO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Data_INVIO&quot;,&quot;type&quot;:&quot;Date&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Data_INVIO&quot;},{&quot;name&quot;:&quot;Data_Inserimento_DLO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Data Inserimento DLO&quot;,&quot;type&quot;:&quot;DateTime&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Data_Inserimento_DLO&quot;}]},{&quot;name&quot;:&quot;SCRIBA_DLOStorico_MCE__dll&quot;,&quot;label&quot;:&quot;SCRIBA_DLOStorico_MCE&quot;,&quot;eventDateTimeFieldName&quot;:&quot;Data_EVENTO_dateformat&quot;,&quot;type&quot;:&quot;DataLakeObject&quot;,&quot;category&quot;:&quot;Engagement&quot;,&quot;fields&quot;:[{&quot;name&quot;:&quot;TAG_URL_DINAMICO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;TAG_URL_DINAMICO&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_TAG_URL_DINAMICO&quot;},{&quot;name&quot;:&quot;ID_SCRIBA__c&quot;,&quot;isPrimaryKey&quot;:true,&quot;label&quot;:&quot;ID_SCRIBA&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_ID_SCRIBA&quot;},{&quot;name&quot;:&quot;Tipo_EVENTO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Tipo_EVENTO&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Tipo_EVENTO&quot;},{&quot;name&quot;:&quot;CANALE__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;CANALE&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_CANALE&quot;},{&quot;name&quot;:&quot;DES_EVENT_CLASS__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;DES_EVENT_CLASS&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_DES_EVENT_CLASS&quot;},{&quot;name&quot;:&quot;Regione_IP__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Regione_IP&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Regione_IP&quot;},{&quot;name&quot;:&quot;Tipo_CLIENT__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Tipo_CLIENT&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Tipo_CLIENT&quot;},{&quot;name&quot;:&quot;Data_EVENTO_dateformat__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Data_EVENTO_dateformat&quot;,&quot;type&quot;:&quot;DateTime&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Data_EVENTO_dateformat&quot;},{&quot;name&quot;:&quot;cdp_sys_PartitionDate__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;cdp_sys_PartitionDate&quot;,&quot;type&quot;:&quot;DateTime&quot;,&quot;keyQualifierField&quot;:&quot;KQ_cdp_sys_PartitionDate&quot;},{&quot;name&quot;:&quot;Tipo_PIATTAFORMA__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Tipo_PIATTAFORMA&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Tipo_PIATTAFORMA&quot;},{&quot;name&quot;:&quot;EVENT_ID__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;EVENT_ID&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_EVENT_ID&quot;},{&quot;name&quot;:&quot;Nome_CLIENT__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Nome_CLIENT&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Nome_CLIENT&quot;},{&quot;name&quot;:&quot;InternalOrganization__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Internal Organization&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_InternalOrganization&quot;},{&quot;name&quot;:&quot;TAG_URL__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;TAG_URL&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_TAG_URL&quot;},{&quot;name&quot;:&quot;Descrizione_PIATTAFORMA__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Descrizione_PIATTAFORMA&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Descrizione_PIATTAFORMA&quot;},{&quot;name&quot;:&quot;Data_CLICK__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Data_CLICK&quot;,&quot;type&quot;:&quot;DateTime&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Data_CLICK&quot;},{&quot;name&quot;:&quot;Data_INVIO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Data_INVIO&quot;,&quot;type&quot;:&quot;DateTime&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Data_INVIO&quot;},{&quot;name&quot;:&quot;ListID__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;ListID&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_ListID&quot;},{&quot;name&quot;:&quot;Descrizione_CLIENT__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Descrizione_CLIENT&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Descrizione_CLIENT&quot;},{&quot;name&quot;:&quot;Data_EVENTO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Data_EVENTO&quot;,&quot;type&quot;:&quot;DateTime&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Data_EVENTO&quot;},{&quot;name&quot;:&quot;Nazione_IP__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Nazione_IP&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Nazione_IP&quot;},{&quot;name&quot;:&quot;ID_TEMPLATE__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;ID_TEMPLATE&quot;,&quot;type&quot;:&quot;Number&quot;,&quot;keyQualifierField&quot;:&quot;KQ_ID_TEMPLATE&quot;},{&quot;name&quot;:&quot;Citt_IP__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Città_IP&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Citt_IP&quot;},{&quot;name&quot;:&quot;BatchID__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;BatchID&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_BatchID&quot;},{&quot;name&quot;:&quot;Data_APERTURA__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Data_APERTURA&quot;,&quot;type&quot;:&quot;DateTime&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Data_APERTURA&quot;},{&quot;name&quot;:&quot;Timezone_IP__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Timezone_IP&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Timezone_IP&quot;},{&quot;name&quot;:&quot;Nome_PIATTAFORMA__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Nome_PIATTAFORMA&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Nome_PIATTAFORMA&quot;},{&quot;name&quot;:&quot;DES_MACRO_EVENTO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;DES_MACRO_EVENTO&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_DES_MACRO_EVENTO&quot;},{&quot;name&quot;:&quot;cdp_sys_SourceVersion__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;cdp_sys_SourceVersion&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_cdp_sys_SourceVersion&quot;},{&quot;name&quot;:&quot;TAG_ID__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;TAG_ID&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_TAG_ID&quot;},{&quot;name&quot;:&quot;ISP__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;ISP&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_ISP&quot;},{&quot;name&quot;:&quot;Dettagli_EVENTO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Dettagli_EVENTO&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Dettagli_EVENTO&quot;},{&quot;name&quot;:&quot;ContactKey__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;ContactKey&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_ContactKey&quot;},{&quot;name&quot;:&quot;Indirizzo_IP__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Indirizzo_IP&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Indirizzo_IP&quot;},{&quot;name&quot;:&quot;DES_CRMO_COD_TIPO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;DES_CRMO_COD_TIPO&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_DES_CRMO_COD_TIPO&quot;},{&quot;name&quot;:&quot;JobID__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;JobID&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_JobID&quot;},{&quot;name&quot;:&quot;ID_COMUNICAZIONE__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;ID_COMUNICAZIONE&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_ID_COMUNICAZIONE&quot;},{&quot;name&quot;:&quot;DESC_EVENTO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;DESC_EVENTO&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_DESC_EVENTO&quot;},{&quot;name&quot;:&quot;Categoria_EVENTO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Categoria_EVENTO&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Categoria_EVENTO&quot;}]}],&quot;type&quot;:&quot;STL&quot;,&quot;version&quot;:&quot;56.0&quot;,&quot;dmoTokenMap&quot;:{}},&quot;label&quot;:&quot;SCRIBA_PesiRegistro_Complete_pt1&quot;,&quot;type&quot;:&quot;BATCH&quot;}</entityPayload>
    <masterLabel>SCRIBA_PesiRegistro_Complete_pt1</masterLabel>
    <parentDataPackageKitDefinitionName xsi:nil="true"/>
    <sourceObject>SCRIBA_MCE_Complete</sourceObject>
    <sourceObjectType>MktDataTransform</sourceObjectType>
    <templateVersion>1</templateVersion>
</DataKitObjectTemplate>
