<?xml version="1.0" encoding="UTF-8"?>
<WaveXmd xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <dataset xsi:nil="true"/>
    <dimensions>
        <conditionalFormatting>
            <formattingBins>
                <bin>Assegnato</bin>
                <formatValue>#FFFFFF</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>0</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>In gestione</bin>
                <formatValue>#38E6E7</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>1</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>Chiuso</bin>
                <formatValue>#0D34B6</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>2</sortIndex>
            </formattingBins>
            <property>chartColor</property>
            <referenceField>StageName</referenceField>
            <sortIndex xsi:nil="true"/>
            <type>categories</type>
        </conditionalFormatting>
        <field>lens_1_2.ALL</field>
        <isDerived>false</isDerived>
        <sortIndex>0</sortIndex>
    </dimensions>
    <dimensions>
        <conditionalFormatting>
            <formattingBins>
                <bin>Assegnato</bin>
                <formatValue>#FFFFFF</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>0</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>In gestione</bin>
                <formatValue>#38E6E7</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>1</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>Chiuso</bin>
                <formatValue>#0D34B6</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>2</sortIndex>
            </formattingBins>
            <property>chartColor</property>
            <referenceField>StageName</referenceField>
            <sortIndex xsi:nil="true"/>
            <type>categories</type>
        </conditionalFormatting>
        <field>lens_1_1.ALL</field>
        <isDerived>false</isDerived>
        <sortIndex>1</sortIndex>
    </dimensions>
    <dimensions>
        <conditionalFormatting>
            <formattingBins>
                <bin>Assegnato</bin>
                <formatValue>#FFFFFF</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>0</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>In gestione</bin>
                <formatValue>#00B3B4</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>1</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>Chiuso</bin>
                <formatValue>#001668</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>2</sortIndex>
            </formattingBins>
            <property>chartColor</property>
            <referenceField>StageName</referenceField>
            <sortIndex xsi:nil="true"/>
            <type>categories</type>
        </conditionalFormatting>
        <field>lens_1_3.ALL</field>
        <isDerived>false</isDerived>
        <sortIndex>2</sortIndex>
    </dimensions>
    <dimensions>
        <conditionalFormatting>
            <formattingBins>
                <bin>Con vendita</bin>
                <formatValue>#963CE9</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>0</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>Senza vendita</bin>
                <formatValue>rgb(87, 198, 233)</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>1</sortIndex>
            </formattingBins>
            <property>chartColor</property>
            <referenceField>closure_status_formula</referenceField>
            <sortIndex xsi:nil="true"/>
            <type>categories</type>
        </conditionalFormatting>
        <field>lens_2.ALL</field>
        <isDerived>false</isDerived>
        <sortIndex>3</sortIndex>
    </dimensions>
    <dimensions>
        <conditionalFormatting>
            <formattingBins>
                <bin>Con vendita</bin>
                <formatValue>#963CE9</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>0</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>Senza vendita</bin>
                <formatValue>rgb(87, 198, 233)</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>1</sortIndex>
            </formattingBins>
            <property>chartColor</property>
            <referenceField>closure_status_formula</referenceField>
            <sortIndex xsi:nil="true"/>
            <type>categories</type>
        </conditionalFormatting>
        <field>lens_3.ALL</field>
        <isDerived>false</isDerived>
        <sortIndex>4</sortIndex>
    </dimensions>
    <dimensions>
        <conditionalFormatting>
            <formattingBins>
                <bin>Con vendita</bin>
                <formatValue>#963CE9</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>0</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>Senza vendita</bin>
                <formatValue>rgb(87, 198, 233)</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>1</sortIndex>
            </formattingBins>
            <property>chartColor</property>
            <referenceField>closure_status_formula</referenceField>
            <sortIndex xsi:nil="true"/>
            <type>categories</type>
        </conditionalFormatting>
        <field>lens_4.ALL</field>
        <isDerived>false</isDerived>
        <sortIndex>5</sortIndex>
    </dimensions>
    <dimensions>
        <conditionalFormatting>
            <formattingBins>
                <bin>Con vendita</bin>
                <formatValue>#963CE9</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>0</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>Senza vendita</bin>
                <formatValue>rgb(87, 198, 233)</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>1</sortIndex>
            </formattingBins>
            <property>chartColor</property>
            <referenceField>closure_status_formula</referenceField>
            <sortIndex xsi:nil="true"/>
            <type>categories</type>
        </conditionalFormatting>
        <field>lens_5.ALL</field>
        <isDerived>false</isDerived>
        <sortIndex>6</sortIndex>
    </dimensions>
    <dimensions>
        <conditionalFormatting>
            <formattingBins>
                <bin>Con vendita</bin>
                <formatValue>#963CE9</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>0</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>Senza vendita</bin>
                <formatValue>rgb(87, 198, 233)</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>1</sortIndex>
            </formattingBins>
            <property>chartColor</property>
            <referenceField>closure_status_formula</referenceField>
            <sortIndex xsi:nil="true"/>
            <type>categories</type>
        </conditionalFormatting>
        <field>lens_8.ALL</field>
        <isDerived>false</isDerived>
        <sortIndex>7</sortIndex>
    </dimensions>
    <dimensions>
        <conditionalFormatting>
            <formattingBins>
                <bin>Con vendita</bin>
                <formatValue>#963CE9</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>0</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>Senza vendita</bin>
                <formatValue>rgb(66, 164, 233)</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>1</sortIndex>
            </formattingBins>
            <property>chartColor</property>
            <referenceField>closure_status_formula</referenceField>
            <sortIndex xsi:nil="true"/>
            <type>categories</type>
        </conditionalFormatting>
        <field>lens_6.ALL</field>
        <isDerived>false</isDerived>
        <sortIndex>8</sortIndex>
    </dimensions>
    <dimensions>
        <conditionalFormatting>
            <formattingBins>
                <bin>Assegnato</bin>
                <formatValue>#FFFFFF</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>0</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>Chiuso</bin>
                <formatValue>#00B3B4</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>1</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>In gestione</bin>
                <formatValue>#001668</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>2</sortIndex>
            </formattingBins>
            <property>chartColor</property>
            <referenceField>StageName</referenceField>
            <sortIndex xsi:nil="true"/>
            <type>categories</type>
        </conditionalFormatting>
        <field>lens_1.ALL</field>
        <isDerived>false</isDerived>
        <sortIndex>9</sortIndex>
    </dimensions>
    <measures>
        <conditionalFormatting>
            <formattingBins>
                <bin>Assegnato</bin>
                <formatValue>#FFFFFF</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>0</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>In gestione</bin>
                <formatValue>#38E6E7</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>1</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>Chiuso</bin>
                <formatValue>#0D34B6</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>2</sortIndex>
            </formattingBins>
            <property>chartColor</property>
            <referenceField>StageName</referenceField>
            <sortIndex xsi:nil="true"/>
            <type>categories</type>
        </conditionalFormatting>
        <field>lens_1_2.A</field>
        <isDerived>false</isDerived>
        <sortIndex>0</sortIndex>
    </measures>
    <measures>
        <conditionalFormatting>
            <formattingBins>
                <bin>Assegnato</bin>
                <formatValue>#FFFFFF</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>0</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>In gestione</bin>
                <formatValue>#38E6E7</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>1</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>Chiuso</bin>
                <formatValue>#0D34B6</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>2</sortIndex>
            </formattingBins>
            <property>chartColor</property>
            <referenceField>StageName</referenceField>
            <sortIndex xsi:nil="true"/>
            <type>categories</type>
        </conditionalFormatting>
        <field>lens_1_1.A</field>
        <isDerived>false</isDerived>
        <sortIndex>1</sortIndex>
    </measures>
    <measures>
        <conditionalFormatting>
            <formattingBins>
                <bin>Assegnato</bin>
                <formatValue>#FFFFFF</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>0</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>In gestione</bin>
                <formatValue>#00B3B4</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>1</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>Chiuso</bin>
                <formatValue>#001668</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>2</sortIndex>
            </formattingBins>
            <property>chartColor</property>
            <referenceField>StageName</referenceField>
            <sortIndex xsi:nil="true"/>
            <type>categories</type>
        </conditionalFormatting>
        <field>lens_1_3.A</field>
        <isDerived>false</isDerived>
        <sortIndex>2</sortIndex>
    </measures>
    <measures>
        <conditionalFormatting>
            <formattingBins>
                <bin>Con vendita</bin>
                <formatValue>#963CE9</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>0</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>Senza vendita</bin>
                <formatValue>rgb(87, 198, 233)</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>1</sortIndex>
            </formattingBins>
            <property>chartColor</property>
            <referenceField>closure_status_formula</referenceField>
            <sortIndex xsi:nil="true"/>
            <type>categories</type>
        </conditionalFormatting>
        <field>lens_2.A</field>
        <isDerived>false</isDerived>
        <sortIndex>3</sortIndex>
    </measures>
    <measures>
        <conditionalFormatting>
            <formattingBins>
                <bin>Con vendita</bin>
                <formatValue>#963CE9</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>0</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>Senza vendita</bin>
                <formatValue>rgb(87, 198, 233)</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>1</sortIndex>
            </formattingBins>
            <property>chartColor</property>
            <referenceField>closure_status_formula</referenceField>
            <sortIndex xsi:nil="true"/>
            <type>categories</type>
        </conditionalFormatting>
        <field>lens_3.A</field>
        <isDerived>false</isDerived>
        <sortIndex>4</sortIndex>
    </measures>
    <measures>
        <conditionalFormatting>
            <formattingBins>
                <bin>Con vendita</bin>
                <formatValue>#963CE9</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>0</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>Senza vendita</bin>
                <formatValue>rgb(87, 198, 233)</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>1</sortIndex>
            </formattingBins>
            <property>chartColor</property>
            <referenceField>closure_status_formula</referenceField>
            <sortIndex xsi:nil="true"/>
            <type>categories</type>
        </conditionalFormatting>
        <field>lens_4.A</field>
        <isDerived>false</isDerived>
        <sortIndex>5</sortIndex>
    </measures>
    <measures>
        <conditionalFormatting>
            <formattingBins>
                <bin>Con vendita</bin>
                <formatValue>#963CE9</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>0</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>Senza vendita</bin>
                <formatValue>rgb(87, 198, 233)</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>1</sortIndex>
            </formattingBins>
            <property>chartColor</property>
            <referenceField>closure_status_formula</referenceField>
            <sortIndex xsi:nil="true"/>
            <type>categories</type>
        </conditionalFormatting>
        <field>lens_5.A</field>
        <isDerived>false</isDerived>
        <sortIndex>6</sortIndex>
    </measures>
    <measures>
        <conditionalFormatting>
            <formattingBins>
                <bin>Con vendita</bin>
                <formatValue>#963CE9</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>0</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>Senza vendita</bin>
                <formatValue>rgb(87, 198, 233)</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>1</sortIndex>
            </formattingBins>
            <property>chartColor</property>
            <referenceField>closure_status_formula</referenceField>
            <sortIndex xsi:nil="true"/>
            <type>categories</type>
        </conditionalFormatting>
        <field>lens_8.A</field>
        <isDerived>false</isDerived>
        <sortIndex>7</sortIndex>
    </measures>
    <measures>
        <conditionalFormatting>
            <formattingBins>
                <bin>Con vendita</bin>
                <formatValue>#963CE9</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>0</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>Senza vendita</bin>
                <formatValue>rgb(66, 164, 233)</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>1</sortIndex>
            </formattingBins>
            <property>chartColor</property>
            <referenceField>closure_status_formula</referenceField>
            <sortIndex xsi:nil="true"/>
            <type>categories</type>
        </conditionalFormatting>
        <field>lens_6.A</field>
        <isDerived>false</isDerived>
        <sortIndex>8</sortIndex>
    </measures>
    <measures>
        <conditionalFormatting>
            <formattingBins>
                <bin>Assegnato</bin>
                <formatValue>#FFFFFF</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>0</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>Chiuso</bin>
                <formatValue>#00B3B4</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>1</sortIndex>
            </formattingBins>
            <formattingBins>
                <bin>In gestione</bin>
                <formatValue>#001668</formatValue>
                <label xsi:nil="true"/>
                <sortIndex>2</sortIndex>
            </formattingBins>
            <property>chartColor</property>
            <referenceField>StageName</referenceField>
            <sortIndex xsi:nil="true"/>
            <type>categories</type>
        </conditionalFormatting>
        <field>lens_1.A</field>
        <isDerived>false</isDerived>
        <sortIndex>9</sortIndex>
    </measures>
    <type>Asset</type>
    <waveVisualization>Trattative_Lavorate</waveVisualization>
</WaveXmd>
