/**
 * CaseCreationMapper
 *
 * Dedicated mapper between Engine DTOs and Salesforce Case sObjects.
 * Converts neutral CasePayload into concrete Case records, including
 * RecordType assignment and (for child) the ParentId reference.
 */
public with sharing class CaseCreationMapper {

    /**
     * Maps a parent Case payload to a Case sObject, applying the provided RecordTypeId.
     */
    public static Case mapToParentCase(CaseCreationEngine.CasePayload payload, Id recordTypeId) {
        if (payload == null) return null;
        Case caseRecord = new Case();
        applyCommonFields(caseRecord, payload);
        caseRecord.RecordTypeId = recordTypeId;
        return caseRecord;
    }

    /**
     * Maps a child Case payload to a Case sObject, setting RecordTypeId and ParentId.
     */
    public static Case mapToChildCase(
        CaseCreationEngine.CasePayload payload,
        Id recordTypeId,
        Id parentCaseId,
        CaseCreationService.ChildAssignment assignmentTarget
    ) {
        if (payload == null) return null;
        Case caseRecord = new Case();
        applyCommonFields(caseRecord, payload);
        caseRecord.RecordTypeId = recordTypeId;
        caseRecord.ParentId = parentCaseId;
        if (assignmentTarget == CaseCreationService.ChildAssignment.CONTACT_CENTER) {
            Id contactCenterQueueId = resolveContactCenterQueueId();
            if (contactCenterQueueId != null) {
                caseRecord.OwnerId = contactCenterQueueId;
            }
        }
        return caseRecord;
    }

    /**
     * Updates an existing Case with fields from payload (no RecordType/Parent changes).
     */
    public static void updateExistingCase(Case existing, CaseCreationEngine.CasePayload payload) {
        if (existing == null || payload == null) return;
        applyCommonFields(existing, payload);
    }

    private static Id contactCenterQueueIdCache;
    private static Id resolveContactCenterQueueId() {
        if (contactCenterQueueIdCache != null) return contactCenterQueueIdCache;
        try {
            Group queueRecord = [
                SELECT Id FROM Group WHERE Type = 'Queue' AND DeveloperName = 'CC_PARK' LIMIT 1
            ];
            contactCenterQueueIdCache = queueRecord != null ? queueRecord.Id : null;
        } catch (Exception ex) {
            // Swallow and fallback to default owner if queue not accessible/not found
            contactCenterQueueIdCache = null;
        }
        return contactCenterQueueIdCache;
    }

    private static void applyCommonFields(Case target, CaseCreationEngine.CasePayload payload) {
        // Standard fields
        target.AccountId = toId(payload.accountId);
        target.Status = payload.status;
        target.SuppliedPhone = payload.suppliedPhone;
        target.Type = payload.type;
        target.Description = payload.description;

        // Lookups and custom fields via typed properties
        target.Agency__c = toId(payload.agencyId);
        target.AssignedGroup__c = toId(payload.assignedGroupId);
        target.AssignedTo__c = toId(payload.assignedToId);
        target.ClosedDate__c = payload.closedDate;
        target.Opportunity__c = toId(payload.opportunityId);
        target.CommercialAreasOfNeed__c = payload.areaOfNeed;
        target.ContactRequestStep__c = payload.contactRequestStage;
        target.ContactRequestTimeSlot__c = payload.contactRequestTimeSlot;
        target.LeoActivityCode__c = payload.leoActivityCode;
    }

    private static Id toId(String value) {
        return String.isBlank(value) ? null : (Id) value;
    }
}
