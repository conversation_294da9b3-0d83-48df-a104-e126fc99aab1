<?xml version="1.0" encoding="UTF-8"?>
<DataKitObjectTemplate xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <developerName xsi:nil="true"/>
    <entityPayload>{&quot;definition&quot;:{&quot;nodes&quot;:{&quot;OUTPUT0&quot;:{&quot;sources&quot;:[&quot;FILTER0&quot;],&quot;action&quot;:&quot;outputD360&quot;,&quot;parameters&quot;:{&quot;fieldsMappings&quot;:[{&quot;sourceField&quot;:&quot;cdp_sys_SourceVersion__c&quot;,&quot;targetField&quot;:&quot;cdp_sys_SourceVersion__c&quot;},{&quot;sourceField&quot;:&quot;InternalOrganization__c&quot;,&quot;targetField&quot;:&quot;InternalOrganization__c&quot;},{&quot;sourceField&quot;:&quot;DES_CRMO_COD_TIPO__c&quot;,&quot;targetField&quot;:&quot;DES_CRMO_COD_TIPO__c&quot;},{&quot;sourceField&quot;:&quot;CANALE__c&quot;,&quot;targetField&quot;:&quot;CANALE__c&quot;},{&quot;sourceField&quot;:&quot;DES_MACRO_EVENTO__c&quot;,&quot;targetField&quot;:&quot;DES_MACRO_EVENTO__c&quot;},{&quot;sourceField&quot;:&quot;DES_EVENT_CLASS__c&quot;,&quot;targetField&quot;:&quot;DES_EVENT_CLASS__c&quot;},{&quot;sourceField&quot;:&quot;ID_COMUNICAZIONE__c&quot;,&quot;targetField&quot;:&quot;ID_COMUNICAZIONE__c&quot;},{&quot;sourceField&quot;:&quot;Data_EVENTO_dateformat__c&quot;,&quot;targetField&quot;:&quot;Data_EVENTO_dateformat__c&quot;},{&quot;sourceField&quot;:&quot;cdp_sys_PartitionDate__c&quot;,&quot;targetField&quot;:&quot;cdp_sys_PartitionDate__c&quot;},{&quot;sourceField&quot;:&quot;Data_INVIO__c&quot;,&quot;targetField&quot;:&quot;Data_INVIO__c&quot;},{&quot;sourceField&quot;:&quot;Peso_DLO_Appoggio__c&quot;,&quot;targetField&quot;:&quot;Peso_Registro_Comunicazioni__c&quot;},{&quot;sourceField&quot;:&quot;DESC_EVENTO__c&quot;,&quot;targetField&quot;:&quot;DESC_EVENTO__c&quot;},{&quot;sourceField&quot;:&quot;Tipo_EVENTO_DLO_Appoggio__c&quot;,&quot;targetField&quot;:&quot;Tipo_EVENTO_Registro_Comunicazioni__c&quot;},{&quot;sourceField&quot;:&quot;ContactKey__c&quot;,&quot;targetField&quot;:&quot;ContactKey__c&quot;}],&quot;name&quot;:&quot;SCRIBA_Registro_MCE__dll&quot;,&quot;writeMode&quot;:&quot;MERGE&quot;,&quot;type&quot;:&quot;dataLakeObject&quot;,&quot;priority&quot;:0}},&quot;FILTER0&quot;:{&quot;sources&quot;:[&quot;LOAD_DATASET0&quot;],&quot;action&quot;:&quot;filter&quot;,&quot;parameters&quot;:{&quot;filterExpressions&quot;:[{&quot;operands&quot;:[{&quot;argument&quot;:0,&quot;type&quot;:&quot;N_DAYS&quot;},{&quot;argument&quot;:0,&quot;type&quot;:&quot;N_DAYS&quot;}],&quot;field&quot;:&quot;Data_Inserimento_DLO__c&quot;,&quot;type&quot;:&quot;DATETIME&quot;,&quot;operator&quot;:&quot;IN_RANGE&quot;}]}},&quot;LOAD_DATASET1&quot;:{&quot;sources&quot;:[],&quot;action&quot;:&quot;load&quot;,&quot;parameters&quot;:{&quot;fields&quot;:[&quot;KQ_CMRA_TRACKING_ID__c&quot;,&quot;DataSourceObject__c&quot;,&quot;cdp_sys_SourceVersion__c&quot;,&quot;COD_ESITO_Appoggio__c&quot;,&quot;DESC_EVENTO__c&quot;,&quot;CMRA_TRACKING_ID__c&quot;,&quot;InternalOrganization__c&quot;,&quot;CF_PIVA__c&quot;,&quot;DES_CRMO_COD_TIPO__c&quot;,&quot;CANALE__c&quot;,&quot;PESO_Appoggio__c&quot;,&quot;DES_MACRO_EVENTO__c&quot;,&quot;DES_EVENT_CLASS__c&quot;,&quot;DataSource__c&quot;,&quot;Data_INVIO__c&quot;,&quot;cdp_sys_PartitionDate__c&quot;,&quot;Data_Inserimento_DLO__c&quot;],&quot;sampleDetails&quot;:{&quot;dataspace&quot;:&quot;default&quot;,&quot;type&quot;:&quot;TopN&quot;},&quot;dataset&quot;:{&quot;name&quot;:&quot;SCRIBA_DLOAppoggio_CRMA__dll&quot;,&quot;type&quot;:&quot;dataLakeObject&quot;}}},&quot;FILTER1&quot;:{&quot;sources&quot;:[&quot;LOAD_DATASET1&quot;],&quot;action&quot;:&quot;filter&quot;,&quot;parameters&quot;:{&quot;filterExpressions&quot;:[{&quot;operands&quot;:[{&quot;argument&quot;:0,&quot;type&quot;:&quot;N_DAYS&quot;},{&quot;argument&quot;:0,&quot;type&quot;:&quot;N_DAYS&quot;}],&quot;field&quot;:&quot;Data_Inserimento_DLO__c&quot;,&quot;type&quot;:&quot;DATETIME&quot;,&quot;operator&quot;:&quot;IN_RANGE&quot;}]}},&quot;LOAD_DATASET0&quot;:{&quot;sources&quot;:[],&quot;action&quot;:&quot;load&quot;,&quot;parameters&quot;:{&quot;fields&quot;:[&quot;KQ_ID_COMUNICAZIONE__c&quot;,&quot;cdp_sys_SourceVersion__c&quot;,&quot;InternalOrganization__c&quot;,&quot;DES_CRMO_COD_TIPO__c&quot;,&quot;DataSourceObject__c&quot;,&quot;CANALE__c&quot;,&quot;DES_MACRO_EVENTO__c&quot;,&quot;DES_EVENT_CLASS__c&quot;,&quot;ID_COMUNICAZIONE__c&quot;,&quot;Data_EVENTO_dateformat__c&quot;,&quot;cdp_sys_PartitionDate__c&quot;,&quot;DataSource__c&quot;,&quot;Data_INVIO__c&quot;,&quot;Peso_DLO_Appoggio__c&quot;,&quot;DESC_EVENTO__c&quot;,&quot;Tipo_EVENTO_DLO_Appoggio__c&quot;,&quot;ContactKey__c&quot;,&quot;Data_Inserimento_DLO__c&quot;],&quot;sampleDetails&quot;:{&quot;dataspace&quot;:&quot;default&quot;,&quot;type&quot;:&quot;TopN&quot;},&quot;dataset&quot;:{&quot;name&quot;:&quot;SCRIBA_Intermedio_MCE__dll&quot;,&quot;type&quot;:&quot;dataLakeObject&quot;}}},&quot;OUTPUT1&quot;:{&quot;sources&quot;:[&quot;FILTER1&quot;],&quot;action&quot;:&quot;outputD360&quot;,&quot;parameters&quot;:{&quot;fieldsMappings&quot;:[{&quot;sourceField&quot;:&quot;cdp_sys_SourceVersion__c&quot;,&quot;targetField&quot;:&quot;cdp_sys_SourceVersion__c&quot;},{&quot;sourceField&quot;:&quot;COD_ESITO_Appoggio__c&quot;,&quot;targetField&quot;:&quot;COD_ESITO_Registro__c&quot;},{&quot;sourceField&quot;:&quot;DESC_EVENTO__c&quot;,&quot;targetField&quot;:&quot;DESC_EVENTO__c&quot;},{&quot;sourceField&quot;:&quot;CMRA_TRACKING_ID__c&quot;,&quot;targetField&quot;:&quot;CMRA_TRACKING_ID__c&quot;},{&quot;sourceField&quot;:&quot;InternalOrganization__c&quot;,&quot;targetField&quot;:&quot;InternalOrganization__c&quot;},{&quot;sourceField&quot;:&quot;CF_PIVA__c&quot;,&quot;targetField&quot;:&quot;CF_PIVA__c&quot;},{&quot;sourceField&quot;:&quot;DES_CRMO_COD_TIPO__c&quot;,&quot;targetField&quot;:&quot;DES_CRMO_COD_TIPO__c&quot;},{&quot;sourceField&quot;:&quot;CANALE__c&quot;,&quot;targetField&quot;:&quot;CANALE__c&quot;},{&quot;sourceField&quot;:&quot;PESO_Appoggio__c&quot;,&quot;targetField&quot;:&quot;PESO_REGISTRO__c&quot;},{&quot;sourceField&quot;:&quot;DES_MACRO_EVENTO__c&quot;,&quot;targetField&quot;:&quot;DES_MACRO_EVENTO__c&quot;},{&quot;sourceField&quot;:&quot;DES_EVENT_CLASS__c&quot;,&quot;targetField&quot;:&quot;DES_EVENT_CLASS__c&quot;},{&quot;sourceField&quot;:&quot;Data_INVIO__c&quot;,&quot;targetField&quot;:&quot;Data_INVIO__c&quot;},{&quot;sourceField&quot;:&quot;cdp_sys_PartitionDate__c&quot;,&quot;targetField&quot;:&quot;cdp_sys_PartitionDate__c&quot;}],&quot;name&quot;:&quot;SCRIBA_Registro_CRMA__dll&quot;,&quot;writeMode&quot;:&quot;MERGE&quot;,&quot;type&quot;:&quot;dataLakeObject&quot;,&quot;priority&quot;:0}}},&quot;ui&quot;:{&quot;hiddenColumns&quot;:[],&quot;connectors&quot;:[{&quot;source&quot;:&quot;FILTER0&quot;,&quot;target&quot;:&quot;OUTPUT0&quot;},{&quot;source&quot;:&quot;FILTER1&quot;,&quot;target&quot;:&quot;OUTPUT1&quot;},{&quot;source&quot;:&quot;LOAD_DATASET0&quot;,&quot;target&quot;:&quot;FILTER0&quot;},{&quot;source&quot;:&quot;LOAD_DATASET1&quot;,&quot;target&quot;:&quot;FILTER1&quot;}],&quot;nodes&quot;:{&quot;OUTPUT0&quot;:{&quot;top&quot;:112,&quot;left&quot;:392,&quot;description&quot;:&quot;&quot;,&quot;label&quot;:&quot;Scrittura Registro MCE&quot;,&quot;type&quot;:&quot;OUTPUT&quot;},&quot;FILTER0&quot;:{&quot;top&quot;:112,&quot;left&quot;:252,&quot;description&quot;:&quot;&quot;,&quot;label&quot;:&quot;Filtro per Data Odierna&quot;,&quot;type&quot;:&quot;FILTER&quot;},&quot;LOAD_DATASET1&quot;:{&quot;top&quot;:252,&quot;left&quot;:112,&quot;label&quot;:&quot;SCRIBA_DLOAppoggio_CRMA&quot;,&quot;type&quot;:&quot;LOAD_DATASET&quot;,&quot;parameters&quot;:{&quot;sampleSize&quot;:500}},&quot;FILTER1&quot;:{&quot;top&quot;:252,&quot;left&quot;:252,&quot;description&quot;:&quot;&quot;,&quot;label&quot;:&quot;Filtro per Data Odierna&quot;,&quot;type&quot;:&quot;FILTER&quot;},&quot;LOAD_DATASET0&quot;:{&quot;top&quot;:112,&quot;left&quot;:112,&quot;label&quot;:&quot;SCRIBA_Intermedio_MCE&quot;,&quot;type&quot;:&quot;LOAD_DATASET&quot;,&quot;parameters&quot;:{&quot;sampleSize&quot;:500}},&quot;OUTPUT1&quot;:{&quot;top&quot;:252,&quot;left&quot;:392,&quot;description&quot;:&quot;&quot;,&quot;label&quot;:&quot;Scrittura Registro CRMA&quot;,&quot;type&quot;:&quot;OUTPUT&quot;}}},&quot;tokenMap&quot;:{},&quot;dloTokenMap&quot;:{&quot;SCRIBA_DLOAppoggio_CRMA__dll&quot;:&quot;SCRIBA_DLOAppoggio_CRMA__dll&quot;,&quot;SCRIBA_Registro_CRMA__dll&quot;:&quot;SCRIBA_Registro_CRMA__dll&quot;,&quot;SCRIBA_Intermedio_MCE__dll&quot;:&quot;SCRIBA_Intermedio_MCE__dll&quot;,&quot;SCRIBA_Registro_MCE__dll&quot;:&quot;SCRIBA_Registro_MCE__dll&quot;},&quot;Output&quot;:[{&quot;name&quot;:&quot;SCRIBA_Registro_CRMA__dll&quot;,&quot;label&quot;:&quot;SCRIBA_Registro_CRMA&quot;,&quot;eventDateTimeFieldName&quot;:&quot;Data_INVIO&quot;,&quot;type&quot;:&quot;DataLakeObject&quot;,&quot;category&quot;:&quot;Engagement&quot;,&quot;fields&quot;:[{&quot;name&quot;:&quot;cdp_sys_SourceVersion__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;cdp_sys_SourceVersion&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_cdp_sys_SourceVersion&quot;},{&quot;name&quot;:&quot;InternalOrganization__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Internal Organization&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_InternalOrganization&quot;},{&quot;name&quot;:&quot;CANALE__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;CANALE&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_CANALE&quot;},{&quot;name&quot;:&quot;CMRA_TRACKING_ID__c&quot;,&quot;isPrimaryKey&quot;:true,&quot;label&quot;:&quot;CMRA_TRACKING_ID&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_CMRA_TRACKING_ID&quot;},{&quot;name&quot;:&quot;Data_INVIO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Data_INVIO&quot;,&quot;type&quot;:&quot;Date&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Data_INVIO&quot;},{&quot;name&quot;:&quot;cdp_sys_PartitionDate__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;cdp_sys_PartitionDate&quot;,&quot;type&quot;:&quot;Date&quot;,&quot;keyQualifierField&quot;:&quot;KQ_cdp_sys_PartitionDate&quot;},{&quot;name&quot;:&quot;DES_MACRO_EVENTO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;DES_MACRO_EVENTO&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_DES_MACRO_EVENTO&quot;},{&quot;name&quot;:&quot;CF_PIVA__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;CF_PIVA&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_CF_PIVA&quot;},{&quot;name&quot;:&quot;PESO_REGISTRO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;PESO_REGISTRO&quot;,&quot;type&quot;:&quot;Number&quot;,&quot;keyQualifierField&quot;:&quot;KQ_PESO_REGISTRO&quot;},{&quot;name&quot;:&quot;DESC_EVENTO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;DESC_EVENTO&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_DESC_EVENTO&quot;},{&quot;name&quot;:&quot;COD_ESITO_Registro__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;COD_ESITO_Registro&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_COD_ESITO_Registro&quot;},{&quot;name&quot;:&quot;DES_EVENT_CLASS__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;DES_EVENT_CLASS&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_DES_EVENT_CLASS&quot;},{&quot;name&quot;:&quot;DES_CRMO_COD_TIPO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;DES_CRMO_COD_TIPO&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_DES_CRMO_COD_TIPO&quot;}]},{&quot;name&quot;:&quot;SCRIBA_Registro_MCE__dll&quot;,&quot;label&quot;:&quot;SCRIBA_Registro_MCE&quot;,&quot;eventDateTimeFieldName&quot;:&quot;Data_EVENTO_dateformat&quot;,&quot;type&quot;:&quot;DataLakeObject&quot;,&quot;category&quot;:&quot;Engagement&quot;,&quot;fields&quot;:[{&quot;name&quot;:&quot;DES_EVENT_CLASS__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;DES_EVENT_CLASS&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_DES_EVENT_CLASS&quot;},{&quot;name&quot;:&quot;DESC_EVENTO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;DESC_EVENTO&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_DESC_EVENTO&quot;},{&quot;name&quot;:&quot;Peso_Registro_Comunicazioni__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Peso Registro Comunicazioni&quot;,&quot;type&quot;:&quot;Number&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Peso_Registro_Comunicazioni&quot;},{&quot;name&quot;:&quot;DES_CRMO_COD_TIPO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;DES_CRMO_COD_TIPO&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_DES_CRMO_COD_TIPO&quot;},{&quot;name&quot;:&quot;DES_MACRO_EVENTO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;DES_MACRO_EVENTO&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_DES_MACRO_EVENTO&quot;},{&quot;name&quot;:&quot;ContactKey__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;ContactKey&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_ContactKey&quot;},{&quot;name&quot;:&quot;Data_EVENTO_dateformat__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Data_EVENTO_dateformat&quot;,&quot;type&quot;:&quot;DateTime&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Data_EVENTO_dateformat&quot;},{&quot;name&quot;:&quot;cdp_sys_PartitionDate__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;cdp_sys_PartitionDate&quot;,&quot;type&quot;:&quot;DateTime&quot;,&quot;keyQualifierField&quot;:&quot;KQ_cdp_sys_PartitionDate&quot;},{&quot;name&quot;:&quot;ID_COMUNICAZIONE__c&quot;,&quot;isPrimaryKey&quot;:true,&quot;label&quot;:&quot;ID_COMUNICAZIONE&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_ID_COMUNICAZIONE&quot;},{&quot;name&quot;:&quot;Data_INVIO__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Data_INVIO&quot;,&quot;type&quot;:&quot;DateTime&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Data_INVIO&quot;},{&quot;name&quot;:&quot;Tipo_EVENTO_Registro_Comunicazioni__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Tipo_EVENTO Registro Comunicazioni&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_Tipo_EVENTO_Registro_Comunicazioni&quot;},{&quot;name&quot;:&quot;InternalOrganization__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;Internal Organization&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_InternalOrganization&quot;},{&quot;name&quot;:&quot;CANALE__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;CANALE&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_CANALE&quot;},{&quot;name&quot;:&quot;cdp_sys_SourceVersion__c&quot;,&quot;isPrimaryKey&quot;:false,&quot;label&quot;:&quot;cdp_sys_SourceVersion&quot;,&quot;type&quot;:&quot;Text&quot;,&quot;keyQualifierField&quot;:&quot;KQ_cdp_sys_SourceVersion&quot;}]}],&quot;type&quot;:&quot;STL&quot;,&quot;version&quot;:&quot;56.0&quot;,&quot;dmoTokenMap&quot;:{}},&quot;label&quot;:&quot;SCRIBA_Complete_Registro_pt2&quot;,&quot;type&quot;:&quot;BATCH&quot;}</entityPayload>
    <masterLabel>SCRIBA_Complete_Registro_pt2</masterLabel>
    <parentDataPackageKitDefinitionName xsi:nil="true"/>
    <sourceObject>SCRIBA_MCE_Complete_Registro</sourceObject>
    <sourceObjectType>MktDataTransform</sourceObjectType>
    <templateVersion>1</templateVersion>
</DataKitObjectTemplate>
