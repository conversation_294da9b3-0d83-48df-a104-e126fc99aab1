<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;vlcTimeTracking&quot; : {
    &quot;GetPolicies&quot; : null,
    &quot;GetUserDetails&quot; : null
  },
  &quot;vlcPersistentComponent&quot; : { },
  &quot;userTimeZone&quot; : null,
  &quot;userProfile&quot; : null,
  &quot;userName&quot; : null,
  &quot;userId&quot; : null,
  &quot;timeStamp&quot; : null,
  &quot;ContextId&quot; : null
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>GetInsurancePoliciesDetails</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&quot;$Vlocity.UserId&quot;</filterValue>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem13</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>User</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>CurrentUser</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>2.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&quot;&quot;</filterValue>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem10</globalKey>
        <inputFieldName>InsurancePolicy.ActiveDate__c</inputFieldName>
        <inputObjectName>InsurancePolicyParticipant</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>InsurancePolicies</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>3.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountId</filterValue>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem8</globalKey>
        <inputFieldName>PrimaryParticipantAccountId</inputFieldName>
        <inputObjectName>InsurancePolicyParticipant</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>InsurancePolicies</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>var:countOfPolicies 0 &gt; | | | var:InsurancePolicies LIST &apos;InsurancePolicy.RecordType.DeveloperName/\/\/LIKE/\/\/&quot;PU&quot;&apos; FILTER LISTSIZE 0 &gt; &amp;&amp;</formulaConverted>
        <formulaExpression>countOfPolicies &gt; 0 &amp;&amp; LISTSIZE(FILTER(LIST(InsurancePolicies), &apos;InsurancePolicy.RecordType.DeveloperName LIKE &quot;PU&quot;&apos;)) &gt; 0</formulaExpression>
        <formulaResultPath>HasUnicaPolicies</formulaResultPath>
        <formulaSequence>6.0</formulaSequence>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem12</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:otherAgencyPolicies:InsurancePolicy.ReferencePolicyNumber var:otherAgencyPolicies:InsurancePolicy.PolicyBranchCode__c CONCAT</formulaConverted>
        <formulaExpression>CONCAT(otherAgencyPolicies:InsurancePolicy.ReferencePolicyNumber,otherAgencyPolicies:InsurancePolicy.PolicyBranchCode__c)</formulaExpression>
        <formulaResultPath>NomePolizzaOther</formulaResultPath>
        <formulaSequence>5.0</formulaSequence>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem7</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>3.0</filterGroup>
        <filterOperator>&lt;&gt;</filterOperator>
        <filterValue>&quot;PU_FOLDER&quot;</filterValue>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem2</globalKey>
        <inputFieldName>InsurancePolicy.RecordType.DeveloperName</inputFieldName>
        <inputObjectName>InsurancePolicyParticipant</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>InsurancePolicies</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>2.0</filterGroup>
        <filterOperator>&lt;&gt;</filterOperator>
        <filterValue>&quot;PU_FOLDER&quot;</filterValue>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem5</globalKey>
        <inputFieldName>InsurancePolicy.RecordType.DeveloperName</inputFieldName>
        <inputObjectName>InsurancePolicyParticipant</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>InsurancePolicies</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsCustom9254</globalKey>
        <inputFieldName>InsurancePolicies:InsurancePolicy.NPI__r.Product__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>policies:Product</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>2.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountId</filterValue>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem9</globalKey>
        <inputFieldName>PrimaryParticipantAccountId</inputFieldName>
        <inputObjectName>InsurancePolicyParticipant</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>InsurancePolicies</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem17</globalKey>
        <inputFieldName>InsurancePolicies:InsurancePolicy.ReferencePolicyNumber</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>policies:ReferencePolicyNumber</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem20</globalKey>
        <inputFieldName>otherAgencyPolicies</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Object</outputFieldFormat>
        <outputFieldName>other_agency_policies:policies</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem19</globalKey>
        <inputFieldName>CurrentUser:IdAzienda__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>user_agency_id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem15</globalKey>
        <inputFieldName>NomePolizzaSame</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>same_user_agency_policies:policies</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem21</globalKey>
        <inputFieldName>sameUserAgencyPolicies</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Object</outputFieldFormat>
        <outputFieldName>same_user_agency_policies:policies</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:sameUserAgencyPolicies_temp|1 var:null == var:sameUserAgencyPolicies_temp|1 | var:sameUserAgencyPolicies_temp LIST IF</formulaConverted>
        <formulaExpression>IF(sameUserAgencyPolicies_temp|1 == null, sameUserAgencyPolicies_temp|1, LIST(sameUserAgencyPolicies_temp))</formulaExpression>
        <formulaResultPath>sameUserAgencyPolicies</formulaResultPath>
        <formulaSequence>4.0</formulaSequence>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem11</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Agency &quot;other&quot; == | | | var:InsurancePolicies LIST &apos;InsurancePolicy.Agency__c/\/\/!=/\/\/&quot;&apos; var:CurrentUser:IdAzienda__c + &apos;&quot;&apos; + FILTER LIST var:null IF</formulaConverted>
        <formulaExpression>IF(%Agency% == &quot;other&quot;, LIST(FILTER(LIST(InsurancePolicies), &apos;InsurancePolicy.Agency__c != &quot;&apos; + CurrentUser:IdAzienda__c + &apos;&quot;&apos;)), null)</formulaExpression>
        <formulaResultPath>otherAgencyPolicies_temp</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem6</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem18</globalKey>
        <inputFieldName>InsurancePolicies:InsurancePolicies:InsurancePolicy.NPI__r.PremiumFrequency__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>policies:PremiumFrequency</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem23</globalKey>
        <inputFieldName>HasUnicaPolicies</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>has_unica_policies</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem26</globalKey>
        <inputFieldName>InsurancePolicies:InsurancePolicy.AreasOfNeed__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>policies:AreasOfNeed__c</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem25</globalKey>
        <inputFieldName>InsurancePolicies:Role</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>policies:Role</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem28</globalKey>
        <inputFieldName>InsurancePolicies:InsurancePolicy.CommercialFamily__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>policies:CommercialFamily__c</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem27</globalKey>
        <inputFieldName>InsurancePolicies:InsurancePolicy.NPI__r.ExpirationDate__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>policies:ExpirationDate</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem16</globalKey>
        <inputFieldName>InsurancePolicies:InsurancePolicy.Society__r.ExternalId__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>policies:SocietyCode</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem14</globalKey>
        <inputFieldName>InsurancePolicies:InsurancePolicy.PolicyBranchCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>policies:BranchCode</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem24</globalKey>
        <inputFieldName>InsurancePolicies:InsurancePolicy.NPI__r.GrossWrittenPremium__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>policies:GrossWrittenPremium</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>3.0</filterGroup>
        <filterOperator>&gt;</filterOperator>
        <filterValue>&quot;$Vlocity.TODAY&quot;</filterValue>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem0</globalKey>
        <inputFieldName>InsurancePolicy.ActiveDate__c</inputFieldName>
        <inputObjectName>InsurancePolicyParticipant</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>InsurancePolicies</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:otherAgencyPolicies_temp|1 var:null == var:otherAgencyPolicies_temp|1 | var:otherAgencyPolicies_temp LIST IF</formulaConverted>
        <formulaExpression>IF(otherAgencyPolicies_temp|1 == null, otherAgencyPolicies_temp|1, LIST(otherAgencyPolicies_temp))</formulaExpression>
        <formulaResultPath>otherAgencyPolicies</formulaResultPath>
        <formulaSequence>3.0</formulaSequence>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem3</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem22</globalKey>
        <inputFieldName>InsurancePolicies:InsurancePolicy.Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>policies:Name</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem29</globalKey>
        <inputFieldName>InsurancePolicies:InsurancePolicy.Agency__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>policies:Agency__c</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>2.0</filterGroup>
        <filterOperator>ORDER BY</filterOperator>
        <filterValue>InsurancePolicy.CommercialFamily__c</filterValue>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem1</globalKey>
        <inputObjectName>InsurancePolicyParticipant</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>InsurancePolicies</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Agency &quot;same&quot; == | | | var:InsurancePolicies LIST &apos;InsurancePolicy.Agency__c/\/\/==/\/\/&quot;&apos; var:CurrentUser:IdAzienda__c + &apos;&quot;&apos; + FILTER LIST var:null IF</formulaConverted>
        <formulaExpression>IF(%Agency% == &quot;same&quot;, LIST(FILTER(LIST(InsurancePolicies), &apos;InsurancePolicy.Agency__c == &quot;&apos; + CurrentUser:IdAzienda__c + &apos;&quot;&apos;)), null)</formulaExpression>
        <formulaResultPath>sameUserAgencyPolicies_temp</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>GetInsurancePoliciesDetailsCustom0jI9O000000yRwbUAEItem4</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePoliciesDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;AccountId&quot; : &quot;0019X00000tIvmXQAS&quot;,
  &quot;Agency&quot; : &quot;same&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>GetInsurancePoliciesDetails_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
