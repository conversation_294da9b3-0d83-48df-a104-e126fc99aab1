/**
 * CaseCreationEngine
 *
 * Core, org-agnostic engine that derives Case type and builds
 * neutral Case payloads (DTOs) for parent and child cases.
 *
 * Responsibilities:
 * - Determine whether the current scenario is Case-based (objectType/eventType)
 * - Produce CasePayloads with all business fields populated
 *
 * Non-responsibilities:
 * - No SOQL/DML
 * - No sObject or field API names
 * - No RecordType resolution
 */
public class CaseCreationEngine {
    // Constants
    private static final String STATUS_NEW = 'New';

    // Core DTOs (no sObject or field API names)
    /**
     * RequestActivity: activity attributes coming from upstream request/flow
     * used to enrich Case payloads (no org-specific types).
     */
    public class RequestActivity {
        public String areaOfNeed;
        public String stageName;
        public String timeSlot;
        public String notes;
        public String codDomainActivity;
        public String numeroRicontatto;
    }

    /**
     * Input: normalized data required to build Case payloads.
     * All identifiers are Strings to keep the Engine agnostic from org types.
     */
    public class Input {
        public String customerId;
        public String agencyId;
        public String opportunityId;
        public RequestActivity activity;
        public String objectType;
        public String eventType;
        public String caseType; // optional: if provided, bypass deriveCaseType
        public String assignedToId;
        public String assignedGroupId;
        public Date defaultCloseDate;
        public String suppliedPhone; // already resolved (activity number or fallback)
    }

    /**
     * CasePayload: neutral representation of a Case to be created by the Service.
     */
    public class CasePayload {
        public String accountId;
        public String agencyId;
        public String assignedGroupId;
        public String assignedToId;
        public Date closedDate;
        public String opportunityId;
        public String status;
        public String suppliedPhone;
        public String type;
        public String description;
        public String areaOfNeed;
        public String contactRequestStage;
        public String contactRequestTimeSlot;
        public String leoActivityCode;
    }

    /**
     * Output: composed parent and child payloads and the resolved Case type.
     */
    public class Output {
        public CasePayload parent;
        public CasePayload child;
        public String caseType;
    }

    public static String deriveCaseType(String objectType, String eventType) {
        String normalizedObjectType = normalize(objectType);
        String normalizedEventType = normalize(eventType);

        if (isContactRequest(normalizedObjectType, normalizedEventType)) return 'CallMeBack';
        if (isSavePreventivo(normalizedObjectType, normalizedEventType)) return 'Salva Preventivo';
        if (isAbandonedCart(normalizedEventType)) return 'Carrello Abbandonato';
        if (isShowPrice(normalizedObjectType, normalizedEventType)) return 'Showprice';
        return null;
    }

    /**
     * Maps canonical Case type labels (outputs of deriveCaseType) to numeric priority.
     * 1 = CallMeBack, 2 = Carrello Abbandonato, 3 = Salva Preventivo, 4 = Showprice.
     * Any other label gets default 99.
     */
    public static Integer eventPriority(String typeOrLabel) {
        if (String.isBlank(typeOrLabel)) return 99;
        String norm = typeOrLabel.trim().toUpperCase();
        if (norm == 'CALLMEBACK') return 1;
        if (norm == 'CARRELLO ABBANDONATO') return 2;
        if (norm == 'SALVA PREVENTIVO') return 3;
        if (norm == 'SHOWPRICE') return 4;
        return 99;
    }

    /**
     * Pure mapping: builds parent/child Case payloads without SOQL/DML.
     */
    public static Output buildCasePayloads(Input inputData) {
        if (inputData == null) return null;

        String derivedCaseType = !String.isBlank(inputData.caseType)
            ? inputData.caseType
            : deriveCaseType(inputData.objectType, inputData.eventType);
        if (String.isBlank(derivedCaseType)) return null; // not case-based

        CasePayload parentPayload = buildPayload(inputData, derivedCaseType);
        CasePayload childPayload = buildPayload(inputData, derivedCaseType);

        Output outputData = new Output();
        outputData.parent = parentPayload;
        outputData.child = childPayload;
        outputData.caseType = derivedCaseType;
        return outputData;
    }

    // --- Helpers (pure) ---
    private static String normalize(String value) {
        return value != null ? value.trim().toUpperCase() : null;
    }

    private static Boolean isContactRequest(String normalizedObjectType, String normalizedEventType) {
        return normalizedObjectType == 'ATTIVITA_CONTATTO' && normalizedEventType == 'RICHIESTA_CONTATTO';
    }

    private static Boolean isSavePreventivo(String normalizedObjectType, String normalizedEventType) {
        return (normalizedObjectType == 'PREVENTIVO' || normalizedObjectType == 'TRATTATIVA') && normalizedEventType == 'SALVA_PREVENTIVO';
    }

    private static Boolean isAbandonedCart(String normalizedEventType) {
        return normalizedEventType == 'MANCATO_ACQUISTO_CARRELLO' || normalizedEventType == 'PAGAMENTO_KO' || normalizedEventType == 'POSIZIONE_ABBANDONATA_DA_CARRELLO';
    }

    private static Boolean isShowPrice(String normalizedObjectType, String normalizedEventType) {
        return normalizedEventType == 'SHOW_PRICE' || normalizedObjectType == 'SHOW_PRICE';
    }

    private static void applyActivity(CasePayload target, RequestActivity activity) {
        if (target == null || activity == null) return;
        target.areaOfNeed = activity.areaOfNeed;
        target.contactRequestStage = activity.stageName;
        target.contactRequestTimeSlot = activity.timeSlot;
        target.description = activity.notes;
        target.leoActivityCode = activity.codDomainActivity;
    }

    private static CasePayload buildPayload(Input inputData, String derivedCaseType) {
        CasePayload payload = new CasePayload();
        payload.accountId = inputData.customerId;
        payload.agencyId = inputData.agencyId;
        payload.assignedGroupId = inputData.assignedGroupId;
        payload.assignedToId = inputData.assignedToId;
        payload.closedDate = inputData.defaultCloseDate;
        applyActivity(payload, inputData.activity);
        payload.opportunityId = inputData.opportunityId;
        payload.status = STATUS_NEW;
        payload.suppliedPhone = inputData.suppliedPhone;
        payload.type = derivedCaseType;
        return payload;
    }
}
