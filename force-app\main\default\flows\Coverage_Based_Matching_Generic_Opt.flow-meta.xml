<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Clean_Areas_of_Need</name>
        <label>Clean Areas of Need</label>
        <locationX>1722</locationX>
        <locationY>2306</locationY>
        <actionName>SanitizeMultiPicklistField</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Initialize_Quote_Record</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>input</name>
            <value>
                <elementReference>allAreasOfNeed</elementReference>
            </value>
        </inputParameters>
        <nameSegment>SanitizeMultiPicklistField</nameSegment>
        <offset>0</offset>
        <outputParameters>
            <assignToReference>allAreasOfNeed</assignToReference>
            <name>output</name>
        </outputParameters>
    </actionCalls>
    <actionCalls>
        <name>Clean_Container_Needs</name>
        <label>Clean Container Needs</label>
        <locationX>864</locationX>
        <locationY>5138</locationY>
        <actionName>SanitizeMultiPicklistField</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Check_Container_Needs</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>input</name>
            <value>
                <elementReference>containerNeedFormula</elementReference>
            </value>
        </inputParameters>
        <nameSegment>SanitizeMultiPicklistField</nameSegment>
        <offset>0</offset>
        <outputParameters>
            <assignToReference>actualContainerNeeds</assignToReference>
            <name>output</name>
        </outputParameters>
    </actionCalls>
    <apiVersion>62.0</apiVersion>
    <assignments>
        <name>Add_Bundle</name>
        <label>Add Bundle</label>
        <locationX>556</locationX>
        <locationY>7478</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.Products__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ProductsVar</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunitiesToUpdate</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>containerRecord</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>check_if_Update</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>add_Container_to_List</name>
        <label>add Container to List</label>
        <locationX>556</locationX>
        <locationY>6962</locationY>
        <assignmentItems>
            <assignToReference>OpportunitiesToUpdate</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>containerRecord</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>check_Product_Update</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Add_Coverage_to_Collection</name>
        <label>Add Coverage to Collection</label>
        <locationX>1810</locationX>
        <locationY>2114</locationY>
        <assignmentItems>
            <assignToReference>opportunityCoverageCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>currentCoverageRecord</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Over_Coverages</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Add_Product_To_List</name>
        <label>Add Product To List</label>
        <locationX>556</locationX>
        <locationY>7262</locationY>
        <assignmentItems>
            <assignToReference>OpportunitiesToUpdate</assignToReference>
            <operator>RemoveAll</operator>
            <value>
                <elementReference>OpportunitiesToUpdate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunitiesToUpdate</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>productRecord</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Evaluate_Bundle_Products</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Add_Updated_Coverage_to_Collection</name>
        <label>Add Updated Coverage to Collection</label>
        <locationX>1810</locationX>
        <locationY>3146</locationY>
        <assignmentItems>
            <assignToReference>coveragesToInsert</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_to_Assign_Coverage_to_Quote</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_to_Assign_Coverage_to_Quote</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>ass_Delta_AMount</name>
        <label>ass Delta AMount</label>
        <locationX>732</locationX>
        <locationY>4238</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.Amount</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>finalContainerAmount</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerUpdateNeeded</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Container_Amount_Update</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_AssignedTo</name>
        <label>Assign AssignedTo</label>
        <locationX>776</locationX>
        <locationY>6662</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.AssignedTo__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>intermediaryId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerUpdateNeeded</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>check_Opp_to_Update</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_closed_won_to_Container</name>
        <label>Assign closed won to Container</label>
        <locationX>798</locationX>
        <locationY>1682</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.StageName</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Evaluate_Product</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Default_Output</name>
        <label>Assign Default Output</label>
        <locationX>1194</locationX>
        <locationY>1898</locationY>
        <assignmentItems>
            <assignToReference>flowResponse</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>flowResponseFormula</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Is_Document_Url_blank</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Flow_Error</name>
        <label>Assign Flow Error</label>
        <locationX>1194</locationX>
        <locationY>8786</locationY>
        <assignmentItems>
            <assignToReference>flowError</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.FaultMessage</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Debug_Flag_After_Error</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Flow_Output</name>
        <label>Assign Flow Output</label>
        <locationX>864</locationX>
        <locationY>8354</locationY>
        <assignmentItems>
            <assignToReference>flowResponse</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>flowResponseFormula</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_if_exit</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Flow_Output_2</name>
        <label>Assign Flow Output 2</label>
        <locationX>314</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>flowResponse</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>flowResponseFormula</elementReference>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <name>Assign_Flow_Response_Titolo</name>
        <label>Assign Flow Response Titolo</label>
        <locationX>754</locationX>
        <locationY>890</locationY>
        <assignmentItems>
            <assignToReference>flowResponse</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>flowResponseFormula</elementReference>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <name>assign_Product</name>
        <label>assign Product</label>
        <locationX>754</locationX>
        <locationY>782</locationY>
        <assignmentItems>
            <assignToReference>productRecord.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>get_Opportunity_Emissione_Titolo.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Flow_Response_Titolo</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Quote_Id</name>
        <label>Assign Quote Id</label>
        <locationX>1810</locationX>
        <locationY>3038</locationY>
        <assignmentItems>
            <assignToReference>Loop_to_Assign_Coverage_to_Quote.Quote__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>quoteRecord.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Add_Updated_Coverage_to_Collection</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Container_Amount_Update</name>
        <label>Container Amount Update</label>
        <locationX>732</locationX>
        <locationY>4346</locationY>
        <assignmentItems>
            <assignToReference>productRecord.Amount</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>containerAmount</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>ProductNeededUpdate</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.AreaOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>containerNeeds</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.TakenInChargeSLAExpiryDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>quoteTICDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.WorkingSLAExpiryDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>quoteExpiryDate</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Working_Expiry_Date</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Container_Journey_Step_Update</name>
        <label>Container Journey Step Update</label>
        <locationX>732</locationX>
        <locationY>5654</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.JourneyStep__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>containerOppJourneyStep</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerUpdateNeeded</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>check_Container_Stato_Cliente</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Container_Need_Update</name>
        <label>Container Need Update</label>
        <locationX>732</locationX>
        <locationY>5354</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.OverallAreasOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>actualContainerNeeds</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerUpdateNeeded</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.AreaOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>actualContainerNeeds</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Container_Journey_Step</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Container_Stato_Cliente_Update</name>
        <label>Container Stato Cliente Update</label>
        <locationX>732</locationX>
        <locationY>5954</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.AccountAgencyStatus__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>statoCliente</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerUpdateNeeded</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>check_Product_Stato_Cliente</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Container_TIC_Expiry_Update</name>
        <label>Container TIC Expiry Update</label>
        <locationX>732</locationX>
        <locationY>4946</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.TakenInChargeSLAExpiryDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>quoteTICDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerUpdateNeeded</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Clean_Container_Needs</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Container_Working_Expiry_Update</name>
        <label>Container Working Expiry Update</label>
        <locationX>732</locationX>
        <locationY>4646</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.WorkingSLAExpiryDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>quoteExpiryDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerUpdateNeeded</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_TIC_Expiry_Date</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Evaluate_Aggregates</name>
        <label>Evaluate Aggregates</label>
        <locationX>1810</locationX>
        <locationY>1898</locationY>
        <assignmentItems>
            <assignToReference>allAreasOfNeed</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>areaOfNeedConcatenation</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteAmount</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Over_Coverages.amount</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteStatus</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Coverages.stageName</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Initialize_Coverage_Record</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Initialize_Coverage_Record</name>
        <label>Initialize Coverage Record</label>
        <locationX>1810</locationX>
        <locationY>2006</locationY>
        <assignmentItems>
            <assignToReference>currentCoverageRecord.Amount__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Coverages.amount</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentCoverageRecord.AreaOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Coverages.areaOfNeed</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentCoverageRecord.Asset__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Coverages.asset</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentCoverageRecord.Fractionation__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Coverages.fractionation</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentCoverageRecord.Description__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Coverages.description</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentCoverageRecord.Email__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Coverages.email</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentCoverageRecord.Conventions__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Coverages.conventionCode</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentCoverageRecord.FirstName__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Coverages.firstName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentCoverageRecord.LastName__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Coverages.lastName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentCoverageRecord.BirthDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Coverages.birthDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentCoverageRecord.ExternalId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Coverages.externalId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentCoverageRecord.EngagementPoint__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Coverages.engagementPoint</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentCoverageRecord.MobilePhone__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Coverages.mobilePhone</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentCoverageRecord.FiscalCode__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Coverages.fiscalCode</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentCoverageRecord.Quote__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentCoverageRecord.CorrelationId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentCoverageRecord.SourceSystemIdentifier__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Coverages.sourceSystemIdentifier</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentCoverageRecord.ServiceProviderIdentifier__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Coverages.serviceProviderIdentifier</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentCoverageRecord.PolicyEffectiveDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Coverages.policyEffectiveDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentCoverageRecord.PolicyExpirationDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Coverages.policyExpirationDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentCoverageRecord.TravelStartDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Coverages.travelStartDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentCoverageRecord.TravelEndDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Coverages.travelEndDate</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Add_Coverage_to_Collection</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Initialize_Quote_Record</name>
        <label>Initialize Quote Record</label>
        <locationX>1722</locationX>
        <locationY>2414</locationY>
        <assignmentItems>
            <assignToReference>quoteRecord.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.name</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.CreatedDateTPD__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.createdDateTPD</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.ExternalId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.externalId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.FolderId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.folderId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.AreasOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>allAreasOfNeed</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.QuoteAmount__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>quoteAmount</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.OpportunityId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>productRecord.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.status</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.Rating__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ratingFormula</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.DomainType__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>prodConfig.DomainType__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.ExpirationDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.expirationDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.BillingStreet</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.billingStreet</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.BillingCity</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.billingCity</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.BillingState</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.billingState</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.BillingPostalCode</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.billingPostalCode</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.BillingCountry</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.billingCountry</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.BillingLatitude</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.billingLatitude</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.BillingLongitude</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.billingLongitude</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.QuoteToStreet</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.quoteToStreet</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.QuoteToCity</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.quoteToCity</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.QuoteToState</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.quoteToState</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.QuoteToPostalCode</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.quoteToPostalCode</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.QuoteToCountry</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.quoteToCountry</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.QuoteToLatitude</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.quoteToLatitude</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.QuoteToLongitude</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.quoteToLongitude</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.BillingName</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.billingName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.Email</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.email</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.Phone</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.phone</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.EngagementPoint__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.engagementPoint</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.DomainType__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.domainType</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.CIP__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.cip</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.DocumentURL__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.linkUnica</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.RecordTypeId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Record_Types.InsuranceQuoteRT.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.CorrelationId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.SourceSystemIdentifier__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.sourceSystemIdentifier</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.ServiceProviderIdentifier__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.serviceProviderIdentifier</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.PolicyEffectiveDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.policyEffectiveDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.PolicyExpirationDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.quote.policyExpirationDate</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Engagement_Point</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Product_Stato_Cliente_Update</name>
        <label>Product Stato Cliente Update</label>
        <locationX>732</locationX>
        <locationY>6254</locationY>
        <assignmentItems>
            <assignToReference>productRecord.AccountAgencyStatus__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>statoCliente</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>ProductNeededUpdate</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Is_Container_Update_Needed</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Quote_Status_to_Blank</name>
        <label>Quote Status to Blank</label>
        <locationX>1590</locationX>
        <locationY>2630</locationY>
        <assignmentItems>
            <assignToReference>quoteRecord.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue></stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Create_Quote_Record</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Existing_Quote_Flag</name>
        <label>Set Existing Quote Flag</label>
        <locationX>1194</locationX>
        <locationY>1790</locationY>
        <assignmentItems>
            <assignToReference>quoteExists</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Default_Output</targetReference>
        </connector>
    </assignments>
    <constants>
        <name>defaultEmptyLocator</name>
        <dataType>String</dataType>
        <value>
            <stringValue>-</stringValue>
        </value>
    </constants>
    <constants>
        <name>defaultRecordName</name>
        <dataType>String</dataType>
        <value>
            <stringValue>TEMP</stringValue>
        </value>
    </constants>
    <decisions>
        <name>Check_Container_Amount</name>
        <label>Check Container Amount</label>
        <locationX>864</locationX>
        <locationY>4130</locationY>
        <defaultConnector>
            <targetReference>Check_Working_Expiry_Date</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Do Not Update</defaultConnectorLabel>
        <rules>
            <name>Update_Amount</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>productRecord.Amount</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>containerAmount</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ass_Delta_AMount</targetReference>
            </connector>
            <label>Update Amount</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Container_Journey_Step</name>
        <label>Check Container Journey Step</label>
        <locationX>864</locationX>
        <locationY>5546</locationY>
        <defaultConnector>
            <targetReference>check_Container_Stato_Cliente</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Do Not Update</defaultConnectorLabel>
        <rules>
            <name>Update_Journey_Step</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>containerOppJourneyStep</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>containerRecord.JourneyStep__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Container_Journey_Step_Update</targetReference>
            </connector>
            <label>Update Journey Step</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Container_Needs</name>
        <label>Check Container Needs</label>
        <locationX>864</locationX>
        <locationY>5246</locationY>
        <defaultConnector>
            <targetReference>Check_Container_Journey_Step</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Do Not Update</defaultConnectorLabel>
        <rules>
            <name>Update_Needs</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>startingContainerNeeds</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>actualContainerNeeds</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Container_Need_Update</targetReference>
            </connector>
            <label>Update Needs</label>
        </rules>
    </decisions>
    <decisions>
        <name>check_Container_Stato_Cliente</name>
        <label>check Container Stato Cliente</label>
        <locationX>864</locationX>
        <locationY>5846</locationY>
        <defaultConnector>
            <targetReference>check_Product_Stato_Cliente</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Update_Container_Stato_Cliente</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>containerRecord.AccountAgencyStatus__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>statoCliente</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Container_Stato_Cliente_Update</targetReference>
            </connector>
            <label>Update Container Stato Cliente</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Debug_Flag</name>
        <label>Check Debug Flag</label>
        <locationX>864</locationX>
        <locationY>8054</locationY>
        <defaultConnector>
            <targetReference>Assign_Flow_Output</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Standard Mode</defaultConnectorLabel>
        <rules>
            <name>Debug_Mode</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.FlowSettings__c.DebugMode__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Debug_Driven_Error</targetReference>
            </connector>
            <label>Debug Mode</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Debug_Flag_After_Error</name>
        <label>Check Debug Flag After Error</label>
        <locationX>1194</locationX>
        <locationY>8894</locationY>
        <defaultConnectorLabel>Standard Mode</defaultConnectorLabel>
        <rules>
            <name>Debug</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.FlowSettings__c.DebugMode__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Debug_Driven_Error_Throw</targetReference>
            </connector>
            <label>Debug</label>
        </rules>
    </decisions>
    <decisions>
        <name>check_Domain_Type</name>
        <label>check Domain Type</label>
        <locationX>1084</locationX>
        <locationY>350</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Previdenza_emissione_titolo_incasso</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>input.domainType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ESSIG_VITA_PREVIDENZA</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>get_Opportunity_Emissione_Titolo</targetReference>
            </connector>
            <label>Previdenza</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Engagement_Point</name>
        <label>Check Engagement Point</label>
        <locationX>1722</locationX>
        <locationY>2522</locationY>
        <defaultConnector>
            <targetReference>Create_Quote_Record</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Is_Angezia</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>input.quote.engagementPoint</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Agenzia</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Quote_Status_to_Blank</targetReference>
            </connector>
            <label>Is Agenzia</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_eventType</name>
        <label>Check eventType</label>
        <locationX>182</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>Assign_Flow_Output_2</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>EventType KO</defaultConnectorLabel>
        <rules>
            <name>EventType_OK</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>eventType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>POSIZIONE_ABBANDONATA_DA_CARRELLO</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>eventType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RICHIESTA_CONTATTO</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>eventType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>MANCATO_ACQUISTO_CARRELLO</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>eventType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>PAGAMENTO_KO</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>CallMeBack_Case_Flow</targetReference>
            </connector>
            <label>Event Type OK</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Existing_Quote</name>
        <label>Check Existing Quote</label>
        <locationX>1458</locationX>
        <locationY>1682</locationY>
        <defaultConnector>
            <targetReference>Loop_Over_Coverages</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>New Quote</defaultConnectorLabel>
        <rules>
            <name>Quote_Exists</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Search_Existing_Quote</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Existing_Quote_Flag</targetReference>
            </connector>
            <label>Quote Exists</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_exit</name>
        <label>Check if exit</label>
        <locationX>864</locationX>
        <locationY>8462</locationY>
        <defaultConnector>
            <targetReference>Is_Omnicanale</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Create AssignEvent</defaultConnectorLabel>
        <rules>
            <name>Exit</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>existingContainer</leftValueReference>
                <operator>EqualTo</operator>
            </conditions>
            <label>Exit</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_If_Quotes_Stored</name>
        <label>Check If Quotes Stored</label>
        <locationX>1722</locationX>
        <locationY>3554</locationY>
        <defaultConnector>
            <targetReference>Create_New_Quote_Event</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Found</defaultConnectorLabel>
        <rules>
            <name>Match_Found</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>matchCount</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>quoteExists</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Edit_Quote_Event</targetReference>
            </connector>
            <label>Match Found</label>
        </rules>
    </decisions>
    <decisions>
        <name>check_if_Update</name>
        <label>check if Update</label>
        <locationX>688</locationX>
        <locationY>7670</locationY>
        <defaultConnector>
            <targetReference>Check_Debug_Flag</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>List_Populated</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>OpportunitiesToUpdate</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Opportunity_List</targetReference>
            </connector>
            <label>List Populated</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Object_Type</name>
        <label>Check Object Type</label>
        <locationX>864</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>Upsert_Trattativa_Opt</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Call_Me_Back</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>objectType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>ATTIVITA_CONTATTO</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_eventType</targetReference>
            </connector>
            <label>Call Me Back</label>
        </rules>
        <rules>
            <name>Trattativa</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>objectType</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>ATTIVITA_CONTATTO</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>eventType</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>EMISSIONE_TITOLO_INCASSO</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Upsert_Trattativa_Opt</targetReference>
            </connector>
            <label>Trattativa</label>
        </rules>
        <rules>
            <name>Emissione_Titolo_Incasso</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>eventType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>EMISSIONE_TITOLO_INCASSO</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>check_Domain_Type</targetReference>
            </connector>
            <label>Emissione Titolo Incasso</label>
        </rules>
    </decisions>
    <decisions>
        <name>check_Opp_to_Update</name>
        <label>check Opp to Update</label>
        <locationX>688</locationX>
        <locationY>6854</locationY>
        <defaultConnector>
            <targetReference>check_Product_Update</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>is_COntainer_Update_Needed_list</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>containerUpdateNeeded</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>add_Container_to_List</targetReference>
            </connector>
            <label>is COntainer Update Needed</label>
        </rules>
    </decisions>
    <decisions>
        <name>check_Product_Stato_Cliente</name>
        <label>check Product Stato Cliente</label>
        <locationX>864</locationX>
        <locationY>6146</locationY>
        <defaultConnector>
            <targetReference>Is_Container_Update_Needed</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Update_Product_Stato_Cliente</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>productRecord.AccountAgencyStatus__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>statoCliente</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Product_Stato_Cliente_Update</targetReference>
            </connector>
            <label>Update Product Stato Cliente</label>
        </rules>
    </decisions>
    <decisions>
        <name>check_Product_Update</name>
        <label>check Product Update</label>
        <locationX>688</locationX>
        <locationY>7154</locationY>
        <defaultConnector>
            <targetReference>check_if_Update</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Product_Update_needed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ProductNeededUpdate</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Add_Product_To_List</targetReference>
            </connector>
            <label>Product Update needed</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_TIC_Expiry_Date</name>
        <label>Check TIC Expiry Date</label>
        <locationX>864</locationX>
        <locationY>4838</locationY>
        <defaultConnector>
            <targetReference>Clean_Container_Needs</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Do Not Update</defaultConnectorLabel>
        <rules>
            <name>Update_TIC_Expiry</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>containerRecord.TakenInChargeSLAExpiryDate__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>containerRecord.TakenInChargeSLAExpiryDate__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>quoteTICDate</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Container_TIC_Expiry_Update</targetReference>
            </connector>
            <label>Update TIC Expiry</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Working_Expiry_Date</name>
        <label>Check Working Expiry Date</label>
        <locationX>864</locationX>
        <locationY>4538</locationY>
        <defaultConnector>
            <targetReference>Check_TIC_Expiry_Date</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Do Not Update</defaultConnectorLabel>
        <rules>
            <name>Update_Working_Expiry</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>containerRecord.WorkingSLAExpiryDate__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>containerRecord.WorkingSLAExpiryDate__c</leftValueReference>
                <operator>LessThan</operator>
                <rightValue>
                    <elementReference>quoteExpiryDate</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Container_Working_Expiry_Update</targetReference>
            </connector>
            <label>Update Working Expiry</label>
        </rules>
    </decisions>
    <decisions>
        <name>checkChannel</name>
        <label>checkChannel</label>
        <locationX>864</locationX>
        <locationY>1466</locationY>
        <defaultConnector>
            <targetReference>Search_Existing_Quote</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Previdenza</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>input.channel</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>Previdenza</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Manage_Quote_Previdenza</targetReference>
            </connector>
            <label>Previdenza</label>
        </rules>
        <rules>
            <name>ESSIG</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>input.channel</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>ESSIG</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>is_Polizza_Diretta</targetReference>
            </connector>
            <label>ESSIG</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Container_Update_Needed</name>
        <label>Is Container Update Needed</label>
        <locationX>864</locationX>
        <locationY>6446</locationY>
        <defaultConnector>
            <targetReference>Check_Debug_Flag</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Update</defaultConnectorLabel>
        <rules>
            <name>Update_Needed</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>containerUpdateNeeded</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>IsAssignedToPopulated</targetReference>
            </connector>
            <label>Update Needed</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Document_Url_blank</name>
        <label>Is Document Url blank</label>
        <locationX>1194</locationX>
        <locationY>2006</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Search_Existing_Quote.DocumentURL__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Document_URL_on_Quote</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Omnicanale</name>
        <label>Is Omnicanale</label>
        <locationX>1194</locationX>
        <locationY>8570</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>IsOmnicanale</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>containerRecord.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Omnicanale</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Assignment_Event</targetReference>
            </connector>
            <label>Is Omnicanale</label>
        </rules>
    </decisions>
    <decisions>
        <name>is_Opportunity_Found</name>
        <label>is Opportunity Found</label>
        <locationX>886</locationX>
        <locationY>566</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Opportunity_found</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>get_Opportunity_Emissione_Titolo.Id</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Opportunity_Emissione_Titolo</targetReference>
            </connector>
            <label>Opportunity found</label>
        </rules>
    </decisions>
    <decisions>
        <name>is_Polizza_Diretta</name>
        <label>is Polizza Diretta</label>
        <locationX>666</locationX>
        <locationY>1574</locationY>
        <defaultConnector>
            <targetReference>Assign_closed_won_to_Container</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>No</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>eventType</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>EMISSIONE_DIRETTA_POLIZZA</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Manage_Quote_ESSIG</targetReference>
            </connector>
            <label>No</label>
        </rules>
    </decisions>
    <decisions>
        <name>IsAssignedToPopulated</name>
        <label>IsAssignedToPopulated</label>
        <locationX>688</locationX>
        <locationY>6554</locationY>
        <defaultConnector>
            <targetReference>Assign_AssignedTo</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>isNotPopulated</defaultConnectorLabel>
        <rules>
            <name>isPopulated</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>containerRecord.AssignedTo__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>check_Opp_to_Update</targetReference>
            </connector>
            <label>isPopulated</label>
        </rules>
    </decisions>
    <description>vers prec 59
nuovi campi Quote e Coverage</description>
    <environments>Default</environments>
    <formulas>
        <name>areaOfNeedConcatenation</name>
        <dataType>String</dataType>
        <expression>{!allAreasOfNeed} + {!Loop_Over_Coverages.areaOfNeed} + &apos;;&apos;</expression>
    </formulas>
    <formulas>
        <name>containerNeedFormula</name>
        <dataType>String</dataType>
        <expression>IF(ISBLANK({!startingContainerNeeds}), {!containerNeeds}, {!startingContainerNeeds} + &apos;;&apos; + {!containerNeeds})</expression>
    </formulas>
    <formulas>
        <name>defaultCloseDate</name>
        <dataType>Date</dataType>
        <expression>DATEVALUE({!input.quote.createdDate}) + 60</expression>
    </formulas>
    <formulas>
        <name>deltaAmount</name>
        <dataType>Currency</dataType>
        <expression>{!containerRecord.Amount} - {!productRecord.Amount}</expression>
        <scale>2</scale>
    </formulas>
    <formulas>
        <name>finalContainerAmount</name>
        <dataType>Currency</dataType>
        <expression>IF(
  ISBLANK({!deltaAmount}) || {!deltaAmount} = 0,
  {!containerAmount},
  {!containerAmount} + {!deltaAmount}
)</expression>
        <scale>2</scale>
    </formulas>
    <formulas>
        <name>productRatingFormula</name>
        <dataType>String</dataType>
        <expression>IF(
{!input.hasCallMeBack}, &apos;Caldissima&apos;,
{!ratingFormula})</expression>
    </formulas>
    <formulas>
        <description>Aggiungere check Call Me Back</description>
        <name>ratingFormula</name>
        <dataType>String</dataType>
        <expression>CASE(
    {!input.quote.status},
    &quot;Show price&quot;, &quot;Tiepida&quot;,
    &quot;Salvataggio preventivo&quot;, &quot;Calda&quot;,
    &quot;Acquisto non concluso&quot;, &quot;Caldissima&quot;,
    &quot;Prospetto previdenziale&quot;, &quot;Tiepida&quot;,
    &quot;Scelta del prodotto&quot;, &quot;Calda&quot;,
    &quot;Offerta previdenziale&quot;, &quot;Caldissima&quot;,
    &quot;Download riepilogo&quot;, &quot;Caldissima&quot;,
    &quot;&quot;
)</expression>
    </formulas>
    <formulas>
        <description>Converte i valori testuali presenti nei campi Rating__c, della trattativa, e productRatingFormula in valori numerici, per consentire il confronto diretto tra i rating e determinare quale rappresenta un livello di interesse commerciale più elevato.</description>
        <name>ratingPriorityFormula</name>
        <dataType>Boolean</dataType>
        <expression>CASE( {!productRatingFormula},
    &quot;Fredda&quot;, 1,
    &quot;Tiepida&quot;, 2,
    &quot;Calda&quot;, 3,
    &quot;Caldissima&quot;, 4,
    0
) &gt; CASE(
    {!containerRecord.Rating__c},
    &quot;Fredda&quot;, 1,
    &quot;Tiepida&quot;, 2,
    &quot;Calda&quot;, 3,
    &quot;Caldissima&quot;, 4,
    0
)</expression>
    </formulas>
    <formulas>
        <name>subjectFormula</name>
        <dataType>String</dataType>
        <expression>&apos;Soggetto:&apos; + {!customer.Name}</expression>
    </formulas>
    <interviewLabel>Coverage-Based Matching Generic Opt {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Coverage-Based Matching Generic Opt</label>
    <loops>
        <name>Loop_Over_Coverages</name>
        <label>Loop Over Coverages</label>
        <locationX>1722</locationX>
        <locationY>1790</locationY>
        <collectionReference>input.quote.coverages</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Evaluate_Aggregates</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Clean_Areas_of_Need</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_to_Assign_Coverage_to_Quote</name>
        <label>Loop to Assign Coverage to Quote</label>
        <locationX>1722</locationX>
        <locationY>2930</locationY>
        <collectionReference>opportunityCoverageCollection</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Quote_Id</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Create_Coverages</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>Create_Assignment_Event</name>
        <label>Create Assignment Event</label>
        <locationX>798</locationX>
        <locationY>8678</locationY>
        <faultConnector>
            <targetReference>Assign_Flow_Error</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>Executor__c</field>
            <value>
                <elementReference>agency.Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <stringValue>Assegnazione in Agenzia</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Opportunity__c</field>
            <value>
                <elementReference>containerRecord.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_Record_Types.ContactHistoryEventRT.Id</elementReference>
            </value>
        </inputAssignments>
        <object>ContactHistory__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Coverages</name>
        <label>Create Coverages</label>
        <locationX>1722</locationX>
        <locationY>3338</locationY>
        <connector>
            <targetReference>Stash_Matching_Quotes</targetReference>
        </connector>
        <inputReference>coveragesToInsert</inputReference>
    </recordCreates>
    <recordCreates>
        <name>Create_Edit_Quote_Event</name>
        <label>Create Edit Quote Event</label>
        <locationX>1590</locationX>
        <locationY>3662</locationY>
        <connector>
            <targetReference>Evaluate_Product</targetReference>
        </connector>
        <inputAssignments>
            <field>Executor__c</field>
            <value>
                <elementReference>subjectFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <stringValue>Modifica Preventivo</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Opportunity__c</field>
            <value>
                <elementReference>containerRecord.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_Record_Types.ContactHistoryEventRT.Id</elementReference>
            </value>
        </inputAssignments>
        <object>ContactHistory__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_New_Quote_Event</name>
        <label>Create New Quote Event</label>
        <locationX>1854</locationX>
        <locationY>3662</locationY>
        <connector>
            <targetReference>Evaluate_Product</targetReference>
        </connector>
        <inputAssignments>
            <field>Executor__c</field>
            <value>
                <elementReference>subjectFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <stringValue>Salvataggio nuovo preventivo</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Opportunity__c</field>
            <value>
                <elementReference>containerRecord.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_Record_Types.ContactHistoryEventRT.Id</elementReference>
            </value>
        </inputAssignments>
        <object>ContactHistory__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Quote_Record</name>
        <label>Create Quote Record</label>
        <locationX>1722</locationX>
        <locationY>2822</locationY>
        <connector>
            <targetReference>Loop_to_Assign_Coverage_to_Quote</targetReference>
        </connector>
        <inputReference>quoteRecord</inputReference>
    </recordCreates>
    <recordCreates>
        <name>Debug_Driven_Error</name>
        <label>Debug Driven Error</label>
        <locationX>732</locationX>
        <locationY>8162</locationY>
        <connector>
            <targetReference>Assign_Flow_Output</targetReference>
        </connector>
        <inputAssignments>
            <field>CloseDate</field>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
        </inputAssignments>
        <inputAssignments>
            <field>StageName</field>
        </inputAssignments>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Debug_Driven_Error_Throw</name>
        <label>Debug Driven Error Throw</label>
        <locationX>1062</locationX>
        <locationY>9002</locationY>
        <inputAssignments>
            <field>CloseDate</field>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
        </inputAssignments>
        <inputAssignments>
            <field>StageName</field>
        </inputAssignments>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>get_Opportunity_Emissione_Titolo</name>
        <label>get Opportunity Emissione Titolo</label>
        <locationX>886</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>is_Opportunity_Found</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Record_Types.ProdottoOppRT.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>StageName</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </filters>
        <filters>
            <field>DomainType__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ESSIG_VITA_PREVIDENZA</stringValue>
            </value>
        </filters>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>customerId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Agency__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>agencyId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Search_Existing_Quote</name>
        <label>Search Existing Quote</label>
        <locationX>1458</locationX>
        <locationY>1574</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Existing_Quote</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ExternalId__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>input.quote.externalId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Quote</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Document_URL_on_Quote</name>
        <label>Update Document URL on Quote</label>
        <locationX>1062</locationX>
        <locationY>2114</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Search_Existing_Quote.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>DocumentURL__c</field>
            <value>
                <elementReference>input.quote.linkUnica</elementReference>
            </value>
        </inputAssignments>
        <object>Quote</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Opportunity_Emissione_Titolo</name>
        <label>Update Opportunity Emissione Titolo</label>
        <locationX>754</locationX>
        <locationY>674</locationY>
        <connector>
            <targetReference>assign_Product</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>get_Opportunity_Emissione_Titolo.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Amount</field>
            <value>
                <elementReference>input.amount</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>StageName</field>
            <value>
                <stringValue>In chiusura</stringValue>
            </value>
        </inputAssignments>
        <object>Opportunity</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Opportunity_List</name>
        <label>Update Opportunity List</label>
        <locationX>556</locationX>
        <locationY>7778</locationY>
        <connector>
            <targetReference>Check_Debug_Flag</targetReference>
        </connector>
        <inputReference>OpportunitiesToUpdate</inputReference>
    </recordUpdates>
    <runInMode>SystemModeWithoutSharing</runInMode>
    <start>
        <locationX>738</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Record_Types</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <name>CallMeBack_Case_Flow</name>
        <label>CallMeBack-Case-Flow</label>
        <locationX>50</locationX>
        <locationY>458</locationY>
        <flowName>CallMeBack_Case_Opt</flowName>
        <inputAssignments>
            <name>agencyId</name>
            <value>
                <elementReference>agencyId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>CallMeBackCaseRT</name>
            <value>
                <elementReference>Get_Record_Types.CallMeBackCaseRT</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>ContactHistoryEventRT</name>
            <value>
                <elementReference>Get_Record_Types.ContactHistoryEventRT</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>contractId</name>
            <value>
                <elementReference>contractId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>customerId</name>
            <value>
                <elementReference>customerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>customerInput</name>
            <value>
                <elementReference>customer</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>eventType</name>
            <value>
                <elementReference>eventType</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>inputActivity</name>
            <value>
                <elementReference>inputActivity</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>inputCip</name>
            <value>
                <elementReference>input.cip</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>InsuranceQuoteRT</name>
            <value>
                <elementReference>Get_Record_Types.InsuranceQuoteRT</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>OmnicanaleOppRT</name>
            <value>
                <elementReference>Get_Record_Types.OmnicanaleOppRT</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>oppConfig</name>
            <value>
                <elementReference>oppConfig</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>ProdottoOppRT</name>
            <value>
                <elementReference>Get_Record_Types.ProdottoOppRT</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>flowError</assignToReference>
            <name>flowError</name>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>flowResponse</assignToReference>
            <name>flowResponse</name>
        </outputAssignments>
    </subflows>
    <subflows>
        <name>Evaluate_Bundle_Products</name>
        <label>Evaluate Bundle Products</label>
        <locationX>556</locationX>
        <locationY>7370</locationY>
        <connector>
            <targetReference>Add_Bundle</targetReference>
        </connector>
        <flowName>Evaluate_Bundle_Products</flowName>
        <inputAssignments>
            <name>BundleOpportunity</name>
            <value>
                <elementReference>containerRecord</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>ProductsList</name>
            <value>
                <elementReference>OpportunitiesToUpdate</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>ProductsVar</assignToReference>
            <name>ProductsOutput</name>
        </outputAssignments>
    </subflows>
    <subflows>
        <name>Evaluate_Product</name>
        <label>Evaluate Product</label>
        <locationX>864</locationX>
        <locationY>4022</locationY>
        <connector>
            <targetReference>Check_Container_Amount</targetReference>
        </connector>
        <flowName>Quote_Opportunity_Evaluation_Opt</flowName>
        <inputAssignments>
            <name>opportunityParent</name>
            <value>
                <elementReference>productRecord</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>targetQuote</name>
            <value>
                <elementReference>quoteRecord</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>containerOppJourneyStep</assignToReference>
            <name>containerOppJourneyStep</name>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>containerNeeds</assignToReference>
            <name>overallAreasOfNeed</name>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>quoteTICDate</assignToReference>
            <name>quoteTICExpiryDate</name>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>quoteExpiryDate</assignToReference>
            <name>quoteWorkingExpiryDate</name>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>containerAmount</assignToReference>
            <name>totalAmount</name>
        </outputAssignments>
    </subflows>
    <subflows>
        <name>Get_Record_Types</name>
        <label>Get Record Types</label>
        <locationX>864</locationX>
        <locationY>134</locationY>
        <connector>
            <targetReference>Check_Object_Type</targetReference>
        </connector>
        <flowName>Get_Record_Types_Coverage_Based_Matching</flowName>
        <inputAssignments>
            <name>QuoteType</name>
            <value>
                <elementReference>input.quote.recordTypeId</elementReference>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <subflows>
        <name>Manage_Quote_ESSIG</name>
        <label>Manage Quote ESSIG</label>
        <locationX>534</locationX>
        <locationY>1682</locationY>
        <connector>
            <targetReference>Evaluate_Product</targetReference>
        </connector>
        <flowName>Coverage_Based_Matching_Upsert_Quote_ESSIG</flowName>
        <inputAssignments>
            <name>Input_Quote_RecordType</name>
            <value>
                <elementReference>Get_Record_Types.InsuranceQuoteRT.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>inputOpportunityWrapper</name>
            <value>
                <elementReference>input</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>QuoteInsuranceRTInput</name>
            <value>
                <elementReference>Get_Record_Types.InsuranceQuoteRT</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>quoteRecord</assignToReference>
            <name>inputQuote</name>
        </outputAssignments>
    </subflows>
    <subflows>
        <name>Manage_Quote_Previdenza</name>
        <label>Manage Quote Previdenza</label>
        <locationX>270</locationX>
        <locationY>1574</locationY>
        <connector>
            <targetReference>Evaluate_Product</targetReference>
        </connector>
        <flowName>Coverage_Based_Matching_Upsert_Quote</flowName>
        <inputAssignments>
            <name>Input_Quote_RecordType</name>
            <value>
                <elementReference>Get_Record_Types.InsuranceQuoteRT.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>inputOpportunityWrapper</name>
            <value>
                <elementReference>input</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>OpportunityProductInput</name>
            <value>
                <elementReference>productRecord</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>QuoteInsuranceRTInput</name>
            <value>
                <elementReference>Get_Record_Types.InsuranceQuoteRT</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>quoteRecord</assignToReference>
            <name>inputQuote</name>
        </outputAssignments>
    </subflows>
    <subflows>
        <name>Stash_Matching_Quotes</name>
        <label>Stash Matching Quotes</label>
        <locationX>1722</locationX>
        <locationY>3446</locationY>
        <connector>
            <targetReference>Check_If_Quotes_Stored</targetReference>
        </connector>
        <flowName>Active_Quote_Management</flowName>
        <inputAssignments>
            <name>targetQuote</name>
            <value>
                <elementReference>quoteRecord</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>matchCount</assignToReference>
            <name>matchCount</name>
        </outputAssignments>
    </subflows>
    <subflows>
        <name>Upsert_Trattativa_Opt</name>
        <label>Upsert Trattativa Opt</label>
        <locationX>864</locationX>
        <locationY>1358</locationY>
        <connector>
            <targetReference>checkChannel</targetReference>
        </connector>
        <flowName>Coverage_Based_Matching_Trattativa_Opt</flowName>
        <inputAssignments>
            <name>agencyId</name>
            <value>
                <elementReference>agencyId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>AgencyInput</name>
            <value>
                <elementReference>agency</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>AgenzialeOppRT</name>
            <value>
                <elementReference>Get_Record_Types.AgenzialeOppRT</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>ContactHistoryEventRT</name>
            <value>
                <elementReference>Get_Record_Types.ContactHistoryEventRT</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>contractId</name>
            <value>
                <elementReference>contractId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>customerId</name>
            <value>
                <elementReference>customerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>CustomerInput</name>
            <value>
                <elementReference>customer</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>eventType</name>
            <value>
                <elementReference>eventType</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>input</name>
            <value>
                <elementReference>input</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>inputActivity</name>
            <value>
                <elementReference>inputActivity</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>intermediaryId</name>
            <value>
                <elementReference>intermediaryId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>objectType</name>
            <value>
                <elementReference>objectType</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>OmnicanaleOppRT</name>
            <value>
                <elementReference>Get_Record_Types.OmnicanaleOppRT</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>oppConfig</name>
            <value>
                <elementReference>oppConfig</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>prodConfig</name>
            <value>
                <elementReference>prodConfig</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>ProdottoOppRT</name>
            <value>
                <elementReference>Get_Record_Types.ProdottoOppRT</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>statoCliente</name>
            <value>
                <elementReference>statoCliente</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>containerRecord</assignToReference>
            <name>containerRecord</name>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>existingContainer</assignToReference>
            <name>existingContainer</name>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>containerUpdateNeeded</assignToReference>
            <name>OpportunityNeedUpdateOutput</name>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>ProductNeededUpdate</assignToReference>
            <name>ProductNeededUpdateOutput</name>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>productRecord</assignToReference>
            <name>productRecord</name>
        </outputAssignments>
    </subflows>
    <textTemplates>
        <name>flowResponseFormula</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>{
&quot;opportunityId&quot;: &quot;{!containerRecord.Id}&quot;,
&quot;productId&quot;: &quot;{!productRecord.Id}&quot;,
&quot;quoteId&quot;: &quot;{!quoteRecord.Id}&quot;
}</text>
    </textTemplates>
    <variables>
        <name>actualContainerNeeds</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>agency</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <name>agencyId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>agencySelectedByLocator</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>allAreasOfNeed</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ATTIVITA_CONTATTO</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>ATTIVITA_CONTATTO</stringValue>
        </value>
    </variables>
    <variables>
        <name>containerAmount</name>
        <dataType>Currency</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
    </variables>
    <variables>
        <name>containerName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>containerNeeds</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>containerOppJourneyStep</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>containerRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>containerUpdateNeeded</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>contractId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>coveragesToInsert</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>OpportunityCoverage__c</objectType>
    </variables>
    <variables>
        <name>currentCoverageRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>OpportunityCoverage__c</objectType>
    </variables>
    <variables>
        <name>customer</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <name>customerId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>eventType</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>existingContainer</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>flowError</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>flowResponse</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>input</name>
        <apexClass>OpportunityWSWrapper</apexClass>
        <dataType>Apex</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>inputActivity</name>
        <apexClass>RequestActivity</apexClass>
        <dataType>Apex</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>intermediaryId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>matchCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>objectType</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>OppAmount</name>
        <dataType>Currency</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
    </variables>
    <variables>
        <name>oppConfig</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>OpportunityConfiguration__mdt</objectType>
    </variables>
    <variables>
        <name>OpportunitiesToUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>opportunityCoverageCollection</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>OpportunityCoverage__c</objectType>
    </variables>
    <variables>
        <name>prodConfig</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>ProductConfiguration__mdt</objectType>
    </variables>
    <variables>
        <name>productName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ProductNeededUpdate</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>productRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>ProductsVar</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>quoteAmount</name>
        <dataType>Currency</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
    </variables>
    <variables>
        <name>quoteExists</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>quoteExpiryDate</name>
        <dataType>DateTime</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>quoteRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Quote</objectType>
    </variables>
    <variables>
        <name>quoteStatus</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>quoteTICDate</name>
        <dataType>DateTime</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>startingContainerNeeds</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>statoCliente</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
