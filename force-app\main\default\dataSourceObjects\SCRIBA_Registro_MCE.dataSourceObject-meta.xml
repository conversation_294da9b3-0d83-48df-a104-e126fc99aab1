<?xml version="1.0" encoding="UTF-8"?>
<DataSourceObject xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <creationType>Custom</creationType>
    <dataSource>SCRIBA_Registro_MCE</dataSource>
    <dataSourceFields>
        <fullName>CANALE</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>CANALE</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_CANALE</keyQualifierName>
        <masterLabel>CANALE</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>cdp_sys_PartitionDate</fullName>
        <datatype>F</datatype>
        <definitionCreationType>System</definitionCreationType>
        <externalName>cdp_sys_PartitionDate</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_cdp_sys_PartitionDate</keyQualifierName>
        <masterLabel>cdp_sys_PartitionDate</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber>1.0</versionNumber>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>cdp_sys_SourceVersion</fullName>
        <datatype>S</datatype>
        <definitionCreationType>System</definitionCreationType>
        <externalName>cdp_sys_SourceVersion</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_cdp_sys_SourceVersion</keyQualifierName>
        <masterLabel>cdp_sys_SourceVersion</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>ContactKey</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>ContactKey</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_ContactKey</keyQualifierName>
        <masterLabel>ContactKey</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Data_EVENTO_dateformat</fullName>
        <datatype>F</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Data_EVENTO_dateformat</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>true</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Data_EVENTO_dateformat</keyQualifierName>
        <masterLabel>Data_EVENTO_dateformat</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber>1.0</versionNumber>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Data_INVIO</fullName>
        <datatype>F</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Data_INVIO</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Data_INVIO</keyQualifierName>
        <masterLabel>Data_INVIO</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber>1.0</versionNumber>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>DataSource</fullName>
        <datatype>S</datatype>
        <definitionCreationType>System</definitionCreationType>
        <externalName>DataSource</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_DataSource</keyQualifierName>
        <masterLabel>Data Source</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>DataSourceObject</fullName>
        <datatype>S</datatype>
        <definitionCreationType>System</definitionCreationType>
        <externalName>DataSourceObject</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_DataSourceObject</keyQualifierName>
        <masterLabel>Data Source Object</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>DES_CRMO_COD_TIPO</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>DES_CRMO_COD_TIPO</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_DES_CRMO_COD_TIPO</keyQualifierName>
        <masterLabel>DES_CRMO_COD_TIPO</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>DES_EVENT_CLASS</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>DES_EVENT_CLASS</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_DES_EVENT_CLASS</keyQualifierName>
        <masterLabel>DES_EVENT_CLASS</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>DES_MACRO_EVENTO</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>DES_MACRO_EVENTO</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_DES_MACRO_EVENTO</keyQualifierName>
        <masterLabel>DES_MACRO_EVENTO</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>DESC_EVENTO</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>DESC_EVENTO</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_DESC_EVENTO</keyQualifierName>
        <masterLabel>DESC_EVENTO</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>ID_COMUNICAZIONE</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>ID_COMUNICAZIONE</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_ID_COMUNICAZIONE</keyQualifierName>
        <masterLabel>ID_COMUNICAZIONE</masterLabel>
        <primaryIndexOrder>1</primaryIndexOrder>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>InternalOrganization</fullName>
        <datatype>S</datatype>
        <definitionCreationType>System</definitionCreationType>
        <externalName>InternalOrganization</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_InternalOrganization</keyQualifierName>
        <masterLabel>Internal Organization</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Peso_Registro_Comunicazioni</fullName>
        <datatype>N</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Peso_Registro_Comunicazioni</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Peso_Registro_Comunicazioni</keyQualifierName>
        <masterLabel>Peso Registro Comunicazioni</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <dataSourceFields>
        <fullName>Tipo_EVENTO_Registro_Comunicazioni</fullName>
        <datatype>S</datatype>
        <definitionCreationType>Custom</definitionCreationType>
        <externalName>Tipo_EVENTO_Registro_Comunicazioni</externalName>
        <isDataRequired>false</isDataRequired>
        <isEventDate>false</isEventDate>
        <isFormula>false</isFormula>
        <isRecordModified>false</isRecordModified>
        <keyQualifierName>KQ_Tipo_EVENTO_Registro_Comunicazioni</keyQualifierName>
        <masterLabel>Tipo_EVENTO Registro Comunicazioni</masterLabel>
        <sequence xsi:nil="true"/>
        <usageTag>NONE</usageTag>
        <versionNumber xsi:nil="true"/>
    </dataSourceFields>
    <masterLabel>SCRIBA_Registro_MCE</masterLabel>
    <objectCategory>Engagement</objectCategory>
    <objectType>Object</objectType>
    <sourceObject>SCRIBA_Registro_MCE__dll</sourceObject>
    <storageType>LOCAL</storageType>
    <templateVersion>1</templateVersion>
</DataSourceObject>
