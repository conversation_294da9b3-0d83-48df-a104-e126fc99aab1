<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>assign_groupId_into_list</name>
        <label>assign groupId into list</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>listGroupId</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>loop_group.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>loop_group</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>assign_idUser_into_list</name>
        <label>assign idUser into list</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>idUsers</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>loop_permission_assigned.AssigneeId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>loop_permission_assigned</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>assign_ownerId_into_list</name>
        <label>assign ownerId into list</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>listIdUserFromGroupMember</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>loop_group_member_from_dev_name.UserOrGroupId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>loop_group_member_from_dev_name</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>assign_user_to_case</name>
        <label>assign user to case</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>loop_Attivit.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>getOperatori.firstSelectedRow.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>attivityToModify</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>loop_Attivit</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>loop_Attivit</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>assignFirstOp</name>
        <label>assignFirstOp</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>collop</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>singleUser</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>checkOperatori</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>assignment_ownerId_into_list</name>
        <label>assignment ownerId into list</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>getOwnerFromActivity</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>loop_get_attivit.OwnerId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>loop_get_attivit</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>assignNotContactCenter</name>
        <label>assignNotContactCenter</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>notContactCenter</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>loop_get_attivit</targetReference>
        </connector>
    </assignments>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>checkActivity</name>
        <label>checkActivity</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>non_selezionati</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>isGetActivity</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>get_Attivita</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>notContactCenter</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>get_Operatori</targetReference>
            </connector>
            <label>isGetActivity</label>
        </rules>
        <rules>
            <name>isNotContactCenter</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>notContactCenter</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>non_contact_center</targetReference>
            </connector>
            <label>isNotContactCenter</label>
        </rules>
    </decisions>
    <decisions>
        <name>checkButton</name>
        <label>checkButton</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Copy_2_of_redirect</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>isConferma</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>buttonCustom.renderNext</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>loop_Attivit</targetReference>
            </connector>
            <label>isConferma</label>
        </rules>
    </decisions>
    <decisions>
        <name>checkOperatori</name>
        <label>checkOperatori</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>nonOperatoriScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>siOperatori</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>get_Operatori</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>modificaAssegnazione</targetReference>
            </connector>
            <label>siOperatori</label>
        </rules>
    </decisions>
    <decisions>
        <name>checkRecordType</name>
        <label>checkRecordType</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>assignNotContactCenter</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>isContactCenter</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>loop_get_attivit.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>get_recordType_Activity.Id</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>assignment_ownerId_into_list</targetReference>
            </connector>
            <label>isContactCenter</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>RiassegnazioneCaseFlow {!$Flow.CurrentDateTime}</interviewLabel>
    <label>RiassegnazioneCaseFlow</label>
    <loops>
        <name>loop_Attivit</name>
        <label>loop Attività</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>get_Attivita</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>assign_user_to_case</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>update_Case</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>loop_get_attivit</name>
        <label>loop get attività</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>get_Attivita</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>checkRecordType</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>get_group</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>loop_group</name>
        <label>loop group</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>get_group</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>assign_groupId_into_list</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>get_group_member</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>loop_group_member_from_dev_name</name>
        <label>loop group member from dev name</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>get_group_member_from_dev_name</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>assign_ownerId_into_list</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>checkActivity</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>loop_permission_assigned</name>
        <label>loop permission assigned</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>get_permission_set_assigned</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>assign_idUser_into_list</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>get_Attivita</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>get_Attivita</name>
        <label>get Attivita</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>loop_get_attivit</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>ids</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>get_first_Operator</name>
        <label>get first Operator</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>assignFirstOp</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>IsActive</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>idUsers</elementReference>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>listIdUserFromGroupMember</elementReference>
            </value>
        </filters>
        <object>User</object>
        <outputReference>singleUser</outputReference>
        <queriedFields>Id</queriedFields>
    </recordLookups>
    <recordLookups>
        <name>get_group</name>
        <label>get group</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>loop_group</targetReference>
        </connector>
        <filterLogic>or</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>CC_ContactCenter1</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>CC_ContactCenter2</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>CC_ContactCenter3</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>get_group_member</name>
        <label>get group member</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>get_group_with_dev_name</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>UserOrGroupId</field>
            <operator>In</operator>
            <value>
                <elementReference>getOwnerFromActivity</elementReference>
            </value>
        </filters>
        <filters>
            <field>GroupId</field>
            <operator>In</operator>
            <value>
                <elementReference>listGroupId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>GroupMember</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>get_group_member_from_dev_name</name>
        <label>get group member from dev name</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>loop_group_member_from_dev_name</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>GroupId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>get_group_with_dev_name.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>GroupMember</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>get_group_with_dev_name</name>
        <label>get group with dev name</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>get_group_member_from_dev_name</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>get_group_member.GroupId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>get_Operatori</name>
        <label>get Operatori</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>get_first_Operator</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>IsActive</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>idUsers</elementReference>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>listIdUserFromGroupMember</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>get_permission_group</name>
        <label>get permission group</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>get_permission_set_assigned</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Operatore_CC</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>PermissionSetGroup</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>get_permission_set_assigned</name>
        <label>get permission set assigned</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>loop_permission_assigned</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>PermissionSetGroupId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>get_permission_group.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>PermissionSetAssignment</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>get_recordType_Activity</name>
        <label>get recordType Activity</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>get_permission_group</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>CC_Contact_Center</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>update_Case</name>
        <label>update Case</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Completed</targetReference>
        </connector>
        <inputReference>attivityToModify</inputReference>
    </recordUpdates>
    <screens>
        <name>Completed</name>
        <label>Completed</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>redirect</targetReference>
        </connector>
        <fields>
            <name>messageCompleted</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 20px;&quot;&gt;Operazione completata con successo.&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Ora puoi tornare nella Home Page&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Copy_1_of_Copy_1_of_redirect</name>
        <label>Copy 1 of Copy 1 of redirect</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>Copy_1_of_Copy_1_of_provaRedirect</name>
            <extensionName>c:cC_RedirectFlowRiassegnazione</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Copy_1_of_redirect</name>
        <label>Copy 1 of redirect</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>Copy_1_of_provaRedirect</name>
            <extensionName>c:cC_RedirectFlowRiassegnazione</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Copy_2_of_redirect</name>
        <label>Copy 2 of redirect</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>Copy_2_of_provaRedirect</name>
            <extensionName>c:cC_RedirectFlowRiassegnazione</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Copy_3_of_redirect</name>
        <label>Copy 3 of redirect</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>Copy_3_of_provaRedirect</name>
            <extensionName>c:cC_RedirectFlowRiassegnazione</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>modificaAssegnazione</name>
        <label>Modifica Assegnazione</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Annulla</backButtonLabel>
        <connector>
            <targetReference>checkButton</targetReference>
        </connector>
        <fields>
            <name>modificaAssegnazioneTitle</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 20px;&quot;&gt;Modifica Assegnazione&lt;/strong&gt;&lt;strong style=&quot;color: rgb(24, 24, 24);&quot;&gt;&lt;span class=&quot;ql-cursor&quot;&gt;﻿&lt;/span&gt;&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <fields>
            <name>modificaAssegnazioneSubTitle</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 14px; font-family: &amp;quot;Segoe UI VSS (Regular)&amp;quot;, &amp;quot;Segoe UI&amp;quot;, -apple-system, BlinkMacSystemFont, Roboto, &amp;quot;Helvetica Neue&amp;quot;, Helvetica, Ubuntu, Arial, sans-serif, &amp;quot;Apple Color Emoji&amp;quot;, &amp;quot;Segoe UI Emoji&amp;quot;, &amp;quot;Segoe UI Symbol&amp;quot;; color: rgb(0, 0, 0);&quot;&gt;&amp;nbsp;&lt;/span&gt;&lt;em style=&quot;background-color: rgb(255, 255, 255); font-size: 14px; font-family: &amp;quot;Segoe UI VSS (Regular)&amp;quot;, &amp;quot;Segoe UI&amp;quot;, -apple-system, BlinkMacSystemFont, Roboto, &amp;quot;Helvetica Neue&amp;quot;, Helvetica, Ubuntu, Arial, sans-serif, &amp;quot;Apple Color Emoji&amp;quot;, &amp;quot;Segoe UI Emoji&amp;quot;, &amp;quot;Segoe UI Symbol&amp;quot;; color: rgb(0, 0, 0);&quot;&gt;Seleziona il nuovo assegnatario per l’attività&lt;/em&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <fields>
            <name>getOperatori</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>User</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Data Table</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>SINGLE_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>get_Operatori</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;FirstName&quot;,&quot;guid&quot;:&quot;column-6360&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;First Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;LastName&quot;,&quot;guid&quot;:&quot;column-1db8&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Last Name&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>isShowSearchBar</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectedRows</name>
                <value>
                    <elementReference>collop</elementReference>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <fields>
            <name>buttonCustom</name>
            <extensionName>runtime_industries_lending:flowFooter</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>buttonLabelCustom</name>
                <value>
                    <stringValue>Indietro</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>buttonLabelNext</name>
                <value>
                    <stringValue>Conferma</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>showPrevious</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>non_contact_center</name>
        <label>non contact center</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Copy_1_of_Copy_1_of_redirect</targetReference>
        </connector>
        <fields>
            <name>Copy_1_of_nonselect</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 20px;&quot;&gt;Hai selezionato Attività che non sono del Contact Center&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>non_selezionati</name>
        <label>non selezionati</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Copy_1_of_redirect</targetReference>
        </connector>
        <fields>
            <name>nonselect</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 20px;&quot;&gt;Non hai selezionato nessuna Attività&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>nonOperatoriScreen</name>
        <label>nonOperatoriScreen</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Copy_3_of_redirect</targetReference>
        </connector>
        <fields>
            <name>nonOp</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255); font-size: 20px;&quot;&gt;Non ci sono operatori disponibili per assegnare un Attività&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <nextOrFinishButtonLabel>Avanti</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>redirect</name>
        <label>redirect</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>provaRedirect</name>
            <extensionName>c:cC_RedirectFlowRiassegnazione</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>get_recordType_Activity</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>attivityToModify</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>collop</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>User</objectType>
    </variables>
    <variables>
        <name>getOwnerFromActivity</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ids</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>idUsers</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>listGroupId</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>listIdUserFromGroupMember</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>notContactCenter</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>singleUser</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>User</objectType>
    </variables>
</Flow>
