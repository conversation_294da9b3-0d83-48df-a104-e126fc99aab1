<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>AssignRecordLabel</excludeButtons>
    <excludeButtons>ChangeRecordType</excludeButtons>
    <excludeButtons>Delete</excludeButtons>
    <excludeButtons>DeleteSeries</excludeButtons>
    <excludeButtons>FollowupEvent</excludeButtons>
    <excludeButtons>FollowupTask</excludeButtons>
    <excludeButtons>PrintableView</excludeButtons>
    <excludeButtons>RetryEngagement</excludeButtons>
    <excludeButtons>ViewSeries</excludeButtons>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Fields</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Status</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Subject</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ActivityDate</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>WhatId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Priority</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>WhoId</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
    </platformActionList>
    <quickActionList/>
    <relatedContent>
        <relatedContentItems>
            <layoutItem>
                <behavior>Readonly</behavior>
                <field>WhoId</field>
            </layoutItem>
        </relatedContentItems>
        <relatedContentItems>
            <layoutItem>
                <behavior>Readonly</behavior>
                <field>WhatId</field>
            </layoutItem>
        </relatedContentItems>
    </relatedContent>
    <relatedObjects>WhoId</relatedObjects>
    <relatedObjects>WhatId</relatedObjects>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h9O00000BxP7J</masterLabel>
        <sizeX>3</sizeX>
        <sizeY>1</sizeY>
        <summaryLayoutItems>
            <field>WhatId</field>
            <posX>0</posX>
            <posY>0</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>OwnerId</field>
            <posX>1</posX>
            <posY>0</posY>
        </summaryLayoutItems>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
