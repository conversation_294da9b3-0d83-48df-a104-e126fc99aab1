/**
 * CaseCreationService
 *
 * Orchestrates Case creation by:
 * - Validating input context
 * - Loading org data (Opportunity, Account) and resolving configuration (RecordTypes)
 * - Delegating mapping/business logic to CaseCreationEngine (core DTOs)
 * - Translating DTO payloads to sObjects and performing DML (Parent then Child)
 */
public without sharing class CaseCreationService {

    public class CaseCreationException extends Exception {}

    // Future-proof: child assignment target for RecordType/Owner dispatch
    public enum ChildAssignment { CONTACT_CENTER, AGENCY }

    private static final String LOG_PREFIX = '[CaseCreationService]';

    // Small holder for record type/assignment resolution
    private class RecordTypeResolution {
        Id parentRecordTypeId;
        Id childRecordTypeId;
        ChildAssignment assignmentTarget;
    }

    /**
     * Context: inputs provided by the caller (e.g., WS) to drive Case creation.
     */
    public class Context {
        public Id opportunityId;
        // Either provide 'caseType' directly OR rely on objectType/eventType mapping
        public String caseType; // optional
        public String objectType; // optional
        public String eventType; // optional
        // Activity is provided only via flat fields
        public String areaOfNeed; // flat activity
        public String stageName;  // flat activity
        public String timeSlot;   // flat activity
        public String notes;      // flat activity
        public String codDomainActivity; // flat activity
        public String numeroRicontatto;  // flat activity
    }

    /**
     * Result: identifiers of created Cases and the resolved Case type.
     */
    public class Result {
        public Id parentCaseId;
        public Id childCaseId;
        public String caseType; // CallMeBack / Showprice / ...
    }

    /**
     * Orchestrates data fetching and DML. Mapping and logic lives in CaseCreationEngine.
     * Returns null if scenario is not Case-based. Throws CaseCreationException on errors.
     */
    public static Result createCases(Context context) {
        validate(context);
        System.debug(LoggingLevel.INFO, LOG_PREFIX +
            ' ENTER createCases oppId=' + (context != null ? context.opportunityId : null) +
            ' caseType=' + (context != null ? context.caseType : null) +
            ' objectType=' + (context != null ? context.objectType : null) +
            ' eventType=' + (context != null ? context.eventType : null)
        );

        String derivedCaseType = resolveDerivedCaseType(context);
        if (String.isBlank(derivedCaseType)) return null;

        Opportunity containerOpportunity = fetchContainerOpportunity(context.opportunityId);

        if (containerOpportunity.AccountId == null) {
            throw new CaseCreationException('Opportunity.AccountId is null for container ' + containerOpportunity.Id);
        }

        RecordTypeResolution rts = resolveRecordTypes(containerOpportunity, context);
        if (rts == null || rts.parentRecordTypeId == null || rts.childRecordTypeId == null) {
            throw new CaseCreationException('Case RecordTypes not found: required DeveloperNames "Primary" (or "Root") and "CC_Contact_Center".');
        }

        Date defaultCloseDate = Date.today().addDays(60);

        // Resolve phone/activity
        CaseCreationEngine.RequestActivity activity = resolveActivity(context, containerOpportunity);
        String resolvedSuppliedPhone = resolveSuppliedPhone(activity, containerOpportunity);

        // Build mapping via Engine (no SOQL/DML)
        CaseCreationEngine.Input inputData = buildEngineInput(
            context,
            containerOpportunity,
            activity,
            derivedCaseType,
            defaultCloseDate,
            resolvedSuppliedPhone
        );

        CaseCreationEngine.Output outputData = CaseCreationEngine.buildCasePayloads(inputData);
        if (outputData == null) return null; // safeguard
        System.debug(LoggingLevel.INFO, LOG_PREFIX + ' Engine payload built type=' + outputData.caseType);

        // Detect existing Root Case (change event scenario)
        Case existingRoot = findExistingRootCase(context.opportunityId);
        System.debug(LoggingLevel.INFO, LOG_PREFIX + ' ChangeEvent=' + String.valueOf(existingRoot != null) + ' existingRootId=' + (existingRoot != null ? existingRoot.Id : null) + ' existingRootType=' + (existingRoot != null ? existingRoot.Type : null));
        return (existingRoot == null)
            ? handleCreate(outputData, rts, derivedCaseType)
            : handleChange(existingRoot, outputData, rts, derivedCaseType);
    }

    private static void validate(Context context) {
        if (context == null) throw new CaseCreationException('Context is required');
        if (context.opportunityId == null) throw new CaseCreationException('opportunityId is required');
        // objectType/eventType may be null if caller wants service to decide that it is non-case scenario
    }

    // Engine holds deriveCaseType; keep service thin

    private static CaseCreationEngine.RequestActivity resolveActivity(Context context, Opportunity containerOpportunity) {
        if (context == null) return null;
        CaseCreationEngine.RequestActivity requestActivity = new CaseCreationEngine.RequestActivity();
        requestActivity.areaOfNeed = context.areaOfNeed;
        requestActivity.stageName = context.stageName;
        requestActivity.timeSlot = context.timeSlot; // no clear Opportunity field -> keep only flat value
        requestActivity.notes = context.notes;
        requestActivity.codDomainActivity = context.codDomainActivity;
        requestActivity.numeroRicontatto = context.numeroRicontatto; // phone is resolved separately as suppliedPhone fallback

        // If everything is blank even after fallbacks, return null
        Boolean allFieldsBlank =
            String.isBlank(requestActivity.areaOfNeed) &&
            String.isBlank(requestActivity.stageName) &&
            String.isBlank(requestActivity.timeSlot) &&
            String.isBlank(requestActivity.notes) &&
            String.isBlank(requestActivity.codDomainActivity) &&
            String.isBlank(requestActivity.numeroRicontatto);
        if (allFieldsBlank) return null;
        return requestActivity;
    }

    private static String resolveDerivedCaseType(Context context) {
        String derivedCaseType = !String.isBlank(context.caseType)
            ? context.caseType
            : CaseCreationEngine.deriveCaseType(context.objectType, context.eventType);
        System.debug(LoggingLevel.INFO, LOG_PREFIX + ' derivedCaseType=' + derivedCaseType);
        return derivedCaseType;
    }

    private static Opportunity fetchContainerOpportunity(Id opportunityId) {
        Opportunity containerOpportunity = [
            SELECT Id, AccountId, Account.Phone, Agency__c, AssignedTo__c, AssignedGroup__c
            FROM Opportunity
            WHERE Id = :opportunityId
            LIMIT 1
        ];
        System.debug(LoggingLevel.INFO, LOG_PREFIX + ' Loaded Opportunity id=' + containerOpportunity.Id +
            ' accountId=' + containerOpportunity.AccountId +
            ' agencyId=' + containerOpportunity.Agency__c +
            ' assignedTo=' + containerOpportunity.AssignedTo__c +
            ' assignedGroup=' + containerOpportunity.AssignedGroup__c
        );
        if (containerOpportunity.AccountId == null) {
            throw new CaseCreationException('Opportunity.AccountId is null for container ' + containerOpportunity.Id);
        }
        return containerOpportunity;
    }

    private static RecordTypeResolution resolveRecordTypes(Opportunity containerOpportunity, Context context) {
        RecordTypeResolution out = new RecordTypeResolution();
        out.assignmentTarget = determineChildAssignment(containerOpportunity, context);

        Map<String, Schema.RecordTypeInfo> rtByDev = Schema.SObjectType.Case.getRecordTypeInfosByDeveloperName();
        if (rtByDev != null) {
            Schema.RecordTypeInfo parent = rtByDev.get('Root');
            if (parent != null) out.parentRecordTypeId = parent.getRecordTypeId();
            out.childRecordTypeId = resolveChildRecordTypeId(out.assignmentTarget, rtByDev);
        }
        System.debug(LoggingLevel.INFO, LOG_PREFIX + ' Resolved RTs parentRT=' + out.parentRecordTypeId + ' childRT=' + out.childRecordTypeId + ' assignmentTarget=' + String.valueOf(out.assignmentTarget));
        return out;
    }

    private static String resolveSuppliedPhone(CaseCreationEngine.RequestActivity activity, Opportunity opp) {
        String resolved = (activity != null && !String.isBlank(activity.numeroRicontatto))
            ? activity.numeroRicontatto : null;
        if (String.isBlank(resolved)) {
            resolved = (opp.Account != null) ? opp.Account.Phone : null;
        }
        System.debug(LoggingLevel.DEBUG, LOG_PREFIX + ' Resolved suppliedPhone present=' + (!String.isBlank(resolved)));
        return resolved;
    }

    private static CaseCreationEngine.Input buildEngineInput(
        Context context,
        Opportunity opp,
        CaseCreationEngine.RequestActivity activity,
        String derivedCaseType,
        Date defaultCloseDate,
        String suppliedPhone
    ) {
        CaseCreationEngine.Input inputData = new CaseCreationEngine.Input();
        inputData.customerId = String.valueOf(opp.AccountId);
        inputData.agencyId = (opp.Agency__c != null) ? String.valueOf(opp.Agency__c) : null;
        inputData.opportunityId = String.valueOf(context.opportunityId);
        inputData.activity = activity;
        inputData.objectType = context.objectType;
        inputData.eventType = context.eventType;
        inputData.caseType = derivedCaseType;
        inputData.assignedToId = (opp.AssignedTo__c != null) ? String.valueOf(opp.AssignedTo__c) : null;
        inputData.assignedGroupId = (opp.AssignedGroup__c != null) ? String.valueOf(opp.AssignedGroup__c) : null;
        inputData.defaultCloseDate = defaultCloseDate;
        inputData.suppliedPhone = suppliedPhone;
        return inputData;
    }

    private static Result handleCreate(CaseCreationEngine.Output outputData, RecordTypeResolution rts, String derivedCaseType) {
        Case parentCase = CaseCreationMapper.mapToParentCase(outputData.parent, rts.parentRecordTypeId);
        insert parentCase;

        Case childCase = CaseCreationMapper.mapToChildCase(
            outputData.child,
            rts.childRecordTypeId,
            parentCase.Id,
            rts.assignmentTarget
        );
        insert childCase;

        Result createRes = new Result();
        createRes.parentCaseId = parentCase.Id;
        createRes.childCaseId = childCase.Id;
        createRes.caseType = derivedCaseType;
        System.debug(LoggingLevel.INFO, LOG_PREFIX + ' CREATE ok parentId=' + parentCase.Id + ' childId=' + childCase.Id);
        return createRes;
    }

    private static Result handleChange(Case existingRoot, CaseCreationEngine.Output outputData, RecordTypeResolution rts, String derivedCaseType) {
        Integer newPriority = CaseCreationEngine.eventPriority(outputData.caseType);
        Integer oldPriority = CaseCreationEngine.eventPriority(existingRoot.Type);
        System.debug(LoggingLevel.INFO, LOG_PREFIX + ' Priorities new=' + newPriority + ' old=' + oldPriority + ' (lower is higher-prio)');
        if (newPriority != null && oldPriority != null && newPriority > oldPriority) {
            // Update root and replace child (DELETE/INSERT)
            CaseCreationMapper.updateExistingCase(existingRoot, outputData.parent);
            update existingRoot;

            List<Case> existingChildren = findExistingChildren(existingRoot.Id);
            System.debug(LoggingLevel.INFO, LOG_PREFIX + ' UPDATE path: replacing children count=' + existingChildren.size());
            if (!existingChildren.isEmpty()) {
                delete existingChildren;
            }

            Case newChild = CaseCreationMapper.mapToChildCase(
                outputData.child,
                rts.childRecordTypeId,
                existingRoot.Id,
                rts.assignmentTarget
            );
            insert newChild;

            Result updRes = new Result();
            updRes.parentCaseId = existingRoot.Id;
            updRes.childCaseId = newChild.Id;
            updRes.caseType = derivedCaseType;
            System.debug(LoggingLevel.INFO, LOG_PREFIX + ' UPDATE ok parentId=' + existingRoot.Id + ' newChildId=' + newChild.Id);
            return updRes;
        } else {
            // Priority not higher (<=): no action
            System.debug(LoggingLevel.INFO, LOG_PREFIX + ' NO-OP: priority(new) <= priority(old)');
            return null;
        }
    }

    private static Case findExistingRootCase(Id opportunityId) {
        List<Case> roots = [
            SELECT Id, Type
            FROM Case
            WHERE Opportunity__c = :opportunityId AND ParentId = null
            ORDER BY CreatedDate DESC
            LIMIT 1
        ];
        return roots.isEmpty() ? null : roots[0];
    }

    private static List<Case> findExistingChildren(Id parentCaseId) {
        return [SELECT Id FROM Case WHERE ParentId = :parentCaseId];
    }

    // Dispatcher stub: determines whether child Case should be Contact Center or Agency assigned.
    // TODO: plug real decision logic (Decision Matrix, metadata, etc.). Current fallback: Contact Center.
    private static ChildAssignment determineChildAssignment(Opportunity containerOpportunity, Context context) {
        return ChildAssignment.CONTACT_CENTER;
    }

    // Maps assignment target to Case RecordType Id. For Agency, attempts a best-effort lookup and falls back to CC.
    private static Id resolveChildRecordTypeId(ChildAssignment target, Map<String, Schema.RecordTypeInfo> recordTypeInfosByDeveloperNameMap) {
        if (recordTypeInfosByDeveloperNameMap == null) return null;
        if (target == ChildAssignment.AGENCY) {
            //TODO: Implement logic to resolve Agency RecordType Id, now fallback is CC_Contact_Center
            Schema.RecordTypeInfo recordTypeInfoAgency = recordTypeInfosByDeveloperNameMap.get('Agency');
            if (recordTypeInfoAgency != null) return recordTypeInfoAgency.getRecordTypeId();
        }
        Schema.RecordTypeInfo recordTypeInfoContactCenter = recordTypeInfosByDeveloperNameMap.get('CC_Contact_Center');
        return recordTypeInfoContactCenter != null ? recordTypeInfoContactCenter.getRecordTypeId() : null;
    }
}
