{"dataSourceLinksInfo": {"enableAutomaticLinking": true, "excludeRelationships": [], "links": []}, "filters": [{"dataset": {"name": "Trattative_Dataset"}, "dependent": true, "fields": ["AmbitoFilter"], "locked": false, "operator": "in"}, {"dependent": true, "fields": ["Status"], "locked": false, "operator": "in", "sobject": "Case"}], "gridLayouts": [{"maxWidth": 1920, "name": "<PERSON><PERSON><PERSON>", "numColumns": 50, "pages": [{"label": "Untitled", "name": "fdc21a2b-b68f-46cd-92d1-49c43fdf950c", "navigationHidden": false, "widgets": [{"colspan": 23, "column": 0, "name": "container_1", "row": 2, "rowspan": 38, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#DDDBDA", "borderEdges": ["all"], "borderRadius": 0, "borderWidth": 1}}, {"colspan": 22, "column": 0, "name": "text_1", "row": 2, "rowspan": 4, "widgetStyle": {"borderEdges": []}}, {"colspan": 6, "column": 15, "name": "listselector_16", "row": 6, "rowspan": 4, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#747474", "borderEdges": ["all"], "borderRadius": 4, "borderWidth": 1}}, {"colspan": 6, "column": 1, "name": "listselector_17", "row": 11, "rowspan": 4, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#747474", "borderEdges": ["all"], "borderRadius": 4, "borderWidth": 1}}, {"colspan": 6, "column": 8, "name": "listselector_3", "row": 6, "rowspan": 4, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#747474", "borderEdges": ["all"], "borderRadius": 4, "borderWidth": 1}}, {"colspan": 21, "column": 0, "name": "chart_2", "row": 16, "rowspan": 20, "widgetStyle": {"borderEdges": []}}, {"colspan": 24, "column": 25, "name": "container_3", "row": 2, "rowspan": 38, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#DDDBDA", "borderEdges": ["all"], "borderRadius": 0, "borderWidth": 1}}, {"colspan": 24, "column": 25, "name": "text_2", "row": 2, "rowspan": 4, "widgetStyle": {"borderEdges": []}}, {"colspan": 6, "column": 40, "name": "listselector_1", "row": 6, "rowspan": 4, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#747474", "borderEdges": ["all"], "borderRadius": 4, "borderWidth": 1}}, {"colspan": 6, "column": 26, "name": "listselector_2", "row": 11, "rowspan": 4, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#747474", "borderEdges": ["all"], "borderRadius": 4, "borderWidth": 1}}, {"colspan": 6, "column": 33, "name": "listselector_4", "row": 6, "rowspan": 4, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#747474", "borderEdges": ["all"], "borderRadius": 4, "borderWidth": 1}}, {"colspan": 21, "column": 25, "name": "chart_19", "row": 16, "rowspan": 20, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#747474", "borderEdges": [], "borderRadius": 0, "borderWidth": 1}}, {"colspan": 6, "column": 1, "name": "listselector_6", "row": 6, "rowspan": 4, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#747474", "borderEdges": ["all"], "borderRadius": 4, "borderWidth": 1}}, {"colspan": 6, "column": 26, "name": "listselector_7", "row": 6, "rowspan": 4, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#747474", "borderEdges": ["all"], "borderRadius": 4, "borderWidth": 1}}, {"colspan": 5, "column": 0, "name": "link_1", "row": 36, "rowspan": 3, "widgetStyle": {"borderEdges": []}}, {"colspan": 5, "column": 25, "name": "link_2", "row": 36, "rowspan": 3, "widgetStyle": {"borderEdges": []}}]}], "rowHeight": "fine", "selectors": [], "style": {"alignmentX": "left", "alignmentY": "top", "backgroundColor": "#FFFFFF", "cellSpacingX": 0, "cellSpacingY": 0, "fit": "original", "gutterColor": "#E6ECF2"}, "version": 1.0}], "layouts": [], "steps": {"Origine_1": {"broadcastFacet": true, "groups": [], "label": "Origine_1", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'StageName' in [\"Chiuso\", \"In gestione\"];\nq = group q by 'Origine';\nq = foreach q generate 'Origine' as 'Origine', count() as 'count';\nq = order q by 'Origine' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": ["AmbitoFilter_2", "period_formula_field_1", "Product__c_1"]}, "selectMode": "single", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["Origine"], "plots": ["count"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "Origine_2": {"broadcastFacet": true, "groups": [], "label": "Origine_2", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'StageName' in [\"Chiuso\"];\nq = group q by 'Origine';\nq = foreach q generate 'Origine' as 'Origine', count() as 'count';\nq = order q by 'Origine' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": ["AmbitoFilter_2", "Product__c_1", "CreateDate_Formula_p_1"]}, "selectMode": "single", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["Origine"], "plots": ["count"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "AmbitoFilter_2": {"broadcastFacet": true, "groups": [], "label": "AmbitoFilter_1", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'StageName' in [\"Chiuso\", \"In gestione\"];\nq = group q by 'AmbitoFilter';\nq = foreach q generate 'AmbitoFilter' as 'AmbitoFilter', count() as 'count';\nq = order q by 'AmbitoFilter' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": ["Product__c_1", "period_formula_field_1", "Origine_1"]}, "selectMode": "single", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["AmbitoFilter"], "plots": ["count"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "Product__c_1_1": {"broadcastFacet": true, "groups": [], "label": "Product__c_2", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'StageName' in [\"Chiuso\"];\nq = group q by 'Product__c';\nq = foreach q generate 'Product__c' as 'Product__c', count() as 'count';\nq = order q by 'Product__c' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": ["AmbitoFilter_1_1", "Origine_2", "CreateDate_Formula_p_1"]}, "selectMode": "multi", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": true, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["Product__c"], "plots": ["count"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "lens_1_2": {"broadcastFacet": true, "datasets": [{"name": "Trattative_Dataset"}], "isGlobal": false, "label": "lens_1", "query": {"aggregateFilters": [], "columnGroups": [], "columnTotals": [], "limit": 2000, "orders": [{"name": "StageName", "filters": [], "ascending": true}], "rowTotals": [], "sourceFilters": {"Trattative_Dataset": {"filters": [["StageName", ["<PERSON><PERSON><PERSON>", "In gestione"], "in"]]}}, "sources": [{"columns": [{"field": ["count", "*"], "name": "A"}, {"field": ["unique", "IsClosed"], "name": "B"}], "filters": [], "groups": ["StageName"], "joins": [], "name": "Trattative_Dataset"}]}, "receiveFacetSource": {"mode": "all", "steps": []}, "selectMode": "single", "sortable": true, "type": "aggregateflex", "useExternalFilters": true, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "centerText": "", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": true, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "showMeasureTitle": true, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "pie", "title": {"fontSize": 16, "subtitleFontSize": 11, "label": "Trattative Lavorate", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "inner": 60, "columnMap": {"trellis": [], "dimension": ["StageName"], "plots": ["A"]}, "showActionMenu": true, "centerValueType": "number", "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "lens_1_1": {"broadcastFacet": true, "datasets": [{"name": "Trattative_Dataset"}], "isGlobal": false, "label": "lens_1", "query": {"aggregateFilters": [], "columnGroups": [], "columnTotals": [], "limit": 2000, "orders": [{"name": "StageName", "filters": [], "ascending": true}], "rowTotals": [], "sourceFilters": {"Trattative_Dataset": {"filters": [["StageName", ["<PERSON><PERSON><PERSON>", "In gestione"], "in"]]}}, "sources": [{"columns": [{"field": ["count", "*"], "name": "A"}, {"field": ["unique", "IsClosed"], "name": "B"}], "filters": [], "groups": ["StageName"], "joins": [], "name": "Trattative_Dataset"}]}, "receiveFacetSource": {"mode": "all", "steps": []}, "selectMode": "single", "sortable": true, "type": "aggregateflex", "useExternalFilters": true, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "centerText": "", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": true, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "showMeasureTitle": true, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "pie", "title": {"fontSize": 16, "subtitleFontSize": 11, "label": "Trattative Lavorate", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "inner": 60, "columnMap": {"trellis": [], "dimension": ["StageName"], "plots": ["A"]}, "showActionMenu": true, "centerValueType": "number", "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "AmbitoFilter_1_1": {"broadcastFacet": true, "groups": [], "label": "AmbitoFilter_2", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'StageName' in [\"Chiuso\"];\nq = group q by 'AmbitoFilter';\nq = foreach q generate 'AmbitoFilter' as 'AmbitoFilter', count() as 'count';\nq = order q by 'AmbitoFilter' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": ["Product__c_1_1", "Origine_2", "CreateDate_Formula_p_1"]}, "selectMode": "single", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["AmbitoFilter"], "plots": ["count"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "lens_1_3": {"broadcastFacet": true, "datasets": [{"name": "Trattative_Dataset"}], "isGlobal": false, "label": "lens_1", "query": {"aggregateFilters": [], "columnGroups": [], "columnTotals": [], "limit": 2000, "orders": [{"name": "StageName", "filters": [], "ascending": true}], "rowTotals": [], "sourceFilters": {"Trattative_Dataset": {"filters": [["StageName", ["<PERSON><PERSON><PERSON>", "In gestione"], "in"]]}}, "sources": [{"columns": [{"field": ["count", "*"], "name": "A"}, {"field": ["unique", "IsClosed"], "name": "B"}], "filters": [], "groups": ["StageName"], "joins": [], "name": "Trattative_Dataset"}]}, "receiveFacetSource": {"mode": "include", "steps": []}, "selectMode": "single", "sortable": true, "type": "aggregateflex", "useExternalFilters": false, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "centerText": "", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": true, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "showMeasureTitle": true, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "pie", "title": {"fontSize": 16, "subtitleFontSize": 11, "label": "Trattative Lavorate", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "inner": 60, "columnMap": {"trellis": [], "dimension": ["StageName"], "plots": ["A"]}, "showActionMenu": true, "centerValueType": "number", "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "period_formula_field_2": {"broadcastFacet": true, "groups": [], "label": "period_formula_field_2", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = group q by 'period_formula_field';\nq = foreach q generate 'period_formula_field' as 'period_formula_field', count() as 'count';\nq = order q by 'period_formula_field' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "all", "steps": []}, "selectMode": "single", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["period_formula_field"], "plots": ["count"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "period_formula_field_1": {"broadcastFacet": true, "groups": [], "label": "period_formula_field_1", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = group q by 'period_formula_field';\nq = foreach q generate 'period_formula_field' as 'period_formula_field', count() as 'count';\nq = order q by 'period_formula_field' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": ["AmbitoFilter_2", "Product__c_1", "Origine_1"]}, "selectMode": "single", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["period_formula_field"], "plots": ["count"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "CreateDate_Formula_p_1": {"broadcastFacet": true, "groups": [], "label": "CreateDate_Formula_p_1", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'StageName' in [\"Chiuso\"];\nq = group q by 'CreateDate_Formula_period';\nq = foreach q generate 'CreateDate_Formula_period' as 'CreateDate_Formula_period', count() as 'count';\nq = order q by 'CreateDate_Formula_period' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": ["AmbitoFilter_1_1", "Product__c_1_1", "Origine_2"]}, "selectMode": "single", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["CreateDate_Formula_period"], "plots": ["count"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "static_1": {"broadcastFacet": true, "columns": {"Number2": {"type": "number"}, "Display": {"type": "string"}}, "label": "Ultimi mesi selection", "selectMode": "single", "type": "staticflex", "values": ["{\"Display\":\"Ultimi 3 mesi\",\"Number2\":3}", "{\"Display\":\"Ultimi 6 mesi\",\"Number2\":6}"]}, "Ambito_2_1": {"broadcastFacet": true, "groups": [], "label": "Ambito_2", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'Rating__c' in [\"Calda\", \"Caldissima\", \"Tiepida\"];\nq = filter q by 'Origine' in [\"Preventivatore digitale Unica\"] && 'StageName' not in [\"Chiuso\"];\nq = group q by 'AmbitoFilter';\nq = foreach q generate 'AmbitoFilter' as 'AmbitoFilter', count() as 'count';\nq = order q by 'AmbitoFilter' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": []}, "selectMode": "single", "sortable": true, "start": "[]", "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "lens_8": {"broadcastFacet": true, "datasets": [{"name": "Trattative_Dataset"}], "isGlobal": false, "query": {"aggregateFilters": [], "columnGroups": [], "columnTotals": [], "limit": 2000, "orders": [{"name": "closure_status_formula", "filters": [], "ascending": true}], "rowTotals": [], "sourceFilters": {}, "sources": [{"columns": [{"field": ["count", "*"], "name": "A"}], "filters": [], "groups": ["closure_status_formula"], "joins": [], "name": "Trattative_Dataset"}]}, "receiveFacetSource": {"mode": "all", "steps": []}, "selectMode": "single", "sortable": true, "type": "aggregateflex", "useExternalFilters": true, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["closure_status_formula"], "plots": ["A"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "lens_5": {"broadcastFacet": true, "datasets": [{"name": "Trattative_Dataset"}], "isGlobal": false, "query": {"aggregateFilters": [], "columnGroups": [], "columnTotals": [], "limit": 2000, "orders": [{"name": "closure_status_formula", "filters": [], "ascending": true}], "rowTotals": [], "sourceFilters": {}, "sources": [{"columns": [{"field": ["count", "*"], "name": "A"}], "filters": [], "groups": ["closure_status_formula"], "joins": [], "name": "Trattative_Dataset"}]}, "receiveFacetSource": {"mode": "all", "steps": []}, "selectMode": "single", "sortable": true, "type": "aggregateflex", "useExternalFilters": true, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["closure_status_formula"], "plots": ["A"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "lens_6": {"broadcastFacet": true, "groups": [], "label": "lens_6", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'StageName' in [\"Chiuso\"];\nq = group q by 'closure_status_formula';\nq = foreach q generate q.'closure_status_formula' as 'closure_status_formula', count(q) as 'A';\nq = order q by 'closure_status_formula' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": ["AmbitoFilter_1_1", "Product__c_1_1", "Origine_2", "CreateDate_Formula_p_1"]}, "selectMode": "single", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": false, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "centerText": "", "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "showMeasureTitle": true, "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "pie", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "inner": 60, "columnMap": {"trellis": [], "dimension": ["closure_status_formula"], "plots": ["A"]}, "showActionMenu": false, "centerValueType": "number", "valueType": "compactNumber", "theme": "wave", "compactDecimalDigits": -1, "applyConditionalFormatting": true}, "type": "chart"}}, "Product__c_1": {"broadcastFacet": true, "groups": [], "label": "Product__c_1", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'StageName' in [\"Chiuso\", \"In gestione\"];\nq = group q by 'Product__c';\nq = foreach q generate 'Product__c' as 'Product__c', count() as 'count';\nq = order q by 'Product__c' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": ["AmbitoFilter_2", "period_formula_field_1", "Origine_1"]}, "selectMode": "multi", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["Product__c"], "plots": ["count"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "lens_3": {"broadcastFacet": true, "datasets": [{"name": "Trattative_Dataset"}], "isGlobal": false, "query": {"aggregateFilters": [], "columnGroups": [], "columnTotals": [], "limit": 2000, "orders": [{"name": "closure_status_formula", "filters": [], "ascending": true}], "rowTotals": [], "sourceFilters": {}, "sources": [{"columns": [{"field": ["count", "*"], "name": "A"}], "filters": [], "groups": ["closure_status_formula"], "joins": [], "name": "Trattative_Dataset"}]}, "receiveFacetSource": {"mode": "all", "steps": []}, "selectMode": "single", "sortable": true, "type": "aggregateflex", "useExternalFilters": true, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["closure_status_formula"], "plots": ["A"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "ClosureSubstatus__c_1": {"broadcastFacet": true, "datasets": [{"name": "Trattative_Dataset"}], "isGlobal": false, "query": {"query": "{\"measures\":[[\"count\",\"*\"]],\"groups\":[\"ClosureSubstatus__c\"]}", "version": -1.0}, "receiveFacetSource": {"mode": "all", "steps": []}, "selectMode": "single", "sortable": true, "type": "aggregateflex", "useExternalFilters": true, "useGlobal": true, "visualizationParameters": {}}, "lens_4": {"broadcastFacet": true, "datasets": [{"name": "Trattative_Dataset"}], "isGlobal": false, "query": {"aggregateFilters": [], "columnGroups": [], "columnTotals": [], "limit": 2000, "orders": [{"name": "closure_status_formula", "filters": [], "ascending": true}], "rowTotals": [], "sourceFilters": {}, "sources": [{"columns": [{"field": ["count", "*"], "name": "A"}], "filters": [], "groups": ["closure_status_formula"], "joins": [], "name": "Trattative_Dataset"}]}, "receiveFacetSource": {"mode": "all", "steps": []}, "selectMode": "single", "sortable": true, "type": "aggregateflex", "useExternalFilters": true, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["closure_status_formula"], "plots": ["A"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "lens_1": {"broadcastFacet": true, "groups": [], "label": "lens_1", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'StageName' in [\"Chiuso\", \"In gestione\"];\nq = group q by 'StageName';\nq = foreach q generate \n        case \n            when q.'StageName' == \"Chiuso\" then \"Chiuse\" \n            else q.'StageName' \n        end as 'StageName',\n        count(q) as 'A';\nq = order q by 'StageName' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": ["Product__c_1", "Origine_1", "period_formula_field_1", "AmbitoFilter_2"]}, "selectMode": "single", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": false, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "centerText": "", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": true, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": true}, "showMeasureTitle": false, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": ["B", "A"], "showNullValues": true, "customizeLegend": true, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": ["StageName"]}}}, "visualizationType": "pie", "title": {"fontSize": 18, "subtitleFontSize": 11, "label": "", "align": "left", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "inner": 60, "showActionMenu": false, "centerValueType": "number", "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "lens_2": {"broadcastFacet": true, "datasets": [{"name": "Trattative_Dataset"}], "isGlobal": false, "query": {"aggregateFilters": [], "columnGroups": [], "columnTotals": [], "limit": 2000, "orders": [{"name": "closure_status_formula", "filters": [], "ascending": true}], "rowTotals": [], "sourceFilters": {}, "sources": [{"columns": [{"field": ["count", "*"], "name": "A"}], "filters": [], "groups": ["closure_status_formula"], "joins": [], "name": "Trattative_Dataset"}]}, "receiveFacetSource": {"mode": "all", "steps": []}, "selectMode": "single", "sortable": true, "type": "aggregateflex", "useExternalFilters": true, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["closure_status_formula"], "plots": ["A"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}}, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#747474", "borderEdges": [], "borderRadius": 0, "borderWidth": 1, "tooltipStyle": {"backgroundColor": "#16325c", "labelColor": "#9faab5", "valueColor": "#ffffff"}}, "widgets": {"listselector_7": {"parameters": {"compact": false, "displayMode": "filter", "exploreLink": false, "filterStyle": {"titleColor": "#747474", "valueColor": "#16325C"}, "instant": true, "interactions": [], "measureField": "count", "showActionMenu": false, "step": "CreateDate_Formula_p_1", "title": "Periodo"}, "type": "listselector"}, "listselector_6": {"parameters": {"compact": false, "displayMode": "filter", "exploreLink": false, "filterStyle": {"titleColor": "#16325C", "valueColor": "#16325C"}, "instant": true, "interactions": [], "measureField": "count", "showActionMenu": false, "step": "period_formula_field_1", "title": "Periodo"}, "type": "listselector"}, "listselector_3": {"parameters": {"compact": false, "displayMode": "filter", "exploreLink": false, "filterStyle": {"titleColor": "#16325C", "valueColor": "#16325C"}, "instant": true, "interactions": [], "measureField": "count", "showActionMenu": false, "step": "Origine_1", "title": "Origine"}, "type": "listselector"}, "listselector_2": {"parameters": {"compact": false, "displayMode": "filter", "exploreLink": false, "filterStyle": {"titleColor": "#16325C", "valueColor": "#16325C"}, "instant": true, "interactions": [], "measureField": "count", "showActionMenu": false, "step": "Product__c_1_1", "title": "<PERSON><PERSON><PERSON>"}, "type": "listselector"}, "text_1": {"parameters": {"content": {"richTextContent": [{"attributes": {"size": "12px", "bold": true}, "insert": "   Trattative Lavorate"}, {"attributes": {"align": "left"}, "insert": "\n"}]}, "interactions": [], "showActionMenu": true}, "type": "text"}, "listselector_4": {"parameters": {"compact": false, "displayMode": "filter", "exploreLink": false, "filterStyle": {"titleColor": "#16325C", "valueColor": "#16325C"}, "instant": true, "interactions": [], "measureField": "count", "showActionMenu": false, "step": "Origine_2", "title": "Origine"}, "type": "listselector"}, "link_1": {"parameters": {"destinationLink": {"tooltipMode": "default", "url": "https://tableau-preprod.gruppounipol.cloud/#/workbooks/323/views"}, "destinationType": "url", "fontSize": 14, "includeState": false, "text": "Vai a Tableau", "textAlignment": "center", "textColor": "#0070D2"}, "type": "link"}, "link_2": {"parameters": {"destinationLink": {"tooltipMode": "default", "url": "https://tableau-preprod.gruppounipol.cloud/#/workbooks/323/views"}, "destinationType": "url", "fontSize": 14, "includeState": false, "text": "Vai a Tableau", "textAlignment": "center", "textColor": "#0070D2"}, "type": "link"}, "container_1": {"parameters": {"alignmentX": "left", "alignmentY": "top", "fit": "original", "interactions": []}, "type": "container"}, "listselector_17": {"parameters": {"compact": false, "displayMode": "filter", "exploreLink": false, "filterStyle": {"titleColor": "#16325C", "valueColor": "#16325C"}, "instant": true, "interactions": [], "measureField": "count", "showActionMenu": false, "step": "Product__c_1", "title": "<PERSON><PERSON><PERSON>"}, "type": "listselector"}, "container_3": {"parameters": {"alignmentX": "left", "alignmentY": "top", "fit": "original", "interactions": []}, "type": "container"}, "chart_19": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "centerText": "", "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "showMeasureTitle": false, "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "pie", "exploreLink": true, "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "inner": 60, "columnMap": {"trellis": [], "dimension": ["closure_status_formula"], "plots": ["A"]}, "showActionMenu": false, "centerValueType": "number", "valueType": "compactNumber", "theme": "wave", "step": "lens_6", "compactDecimalDigits": -1, "applyConditionalFormatting": true}, "type": "chart"}, "text_2": {"parameters": {"content": {"richTextContent": [{"attributes": {"size": "12px", "bold": true}, "insert": "    <PERSON><PERSON><PERSON>"}, {"attributes": {"align": "left"}, "insert": "\n"}]}, "interactions": [], "showActionMenu": true}, "type": "text"}, "listselector_1": {"parameters": {"compact": false, "displayMode": "filter", "exploreLink": false, "filterStyle": {"titleColor": "#16325C", "valueColor": "#16325C"}, "instant": true, "interactions": [], "measureField": "count", "showActionMenu": false, "step": "AmbitoFilter_1_1", "title": "Ambito"}, "type": "listselector"}, "chart_2": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "centerText": "", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": true}, "showMeasureTitle": false, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": ["A"], "showNullValues": true, "customizeLegend": true, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": ["StageName"]}}}, "visualizationType": "pie", "exploreLink": true, "title": {"fontSize": 18, "subtitleFontSize": 11, "label": "", "align": "left", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "inner": 60, "showActionMenu": false, "centerValueType": "number", "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "step": "lens_1", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}, "listselector_16": {"parameters": {"compact": false, "displayMode": "filter", "exploreLink": false, "filterStyle": {"titleColor": "#16325C", "valueColor": "#16325C"}, "instant": true, "interactions": [], "measureField": "count", "showActionMenu": false, "step": "AmbitoFilter_2", "title": "Ambito"}, "type": "listselector"}}}