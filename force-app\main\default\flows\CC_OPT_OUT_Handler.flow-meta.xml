<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Invio_email</name>
        <label>Invio email</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <connector>
            <targetReference>Copy_1_of_checkDecisionButton</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recipientAddresses</name>
        </inputParameters>
        <inputParameters>
            <name>composeEmailContent</name>
            <value>
                <stringValue>True</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <elementReference>subjectEmail</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <stringValue><PERSON><PERSON><PERSON><PERSON>,
segnaliamo richiesta di revoca del consenso commerciale recepita telefonicamente.
Come da procedura vi chiediamo cortesemente di registrarla nei nostri sistemi.
Di seguito i riferimenti del cliente e la nota dell&apos;operatore che ha ricevuto la richiesta.
Nome :        {!get_Attivit.Account.FirstName}
Cognome :         {!get_Attivit.Account.LastName}
Codice Fiscale :         {!get_Attivit.Account.CF__c}
Agenzia :         {!get_Attivit.Agency__r.Name}, {!get_Attivit.Agency__r.Description}, {!get_Attivit.Agency__r.Codice_Agenzia_Madre__c}
Nota :        {!get_Attivit.Activity_Notes__c}

Grazie,
Team Contact Center</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientId</name>
            <value>
                <stringValue>null</stringValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <offset>0</offset>
        <versionString>2.0.1</versionString>
    </actionCalls>
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>assignEmailToSend</name>
        <label>assignEmailToSend</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>EmailToSend</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>loopEmail.EmailAddress__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>loopEmail</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_1_of_modifyEsitoAttivit</name>
        <label>Copy 1 of modifyEsitoAttività</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>get_Attivit.RevocationCC_Sent__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_Attivit.OptOput_Sent__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>updateActivity</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>modifyEsitoAttivit</name>
        <label>modifyEsitoAttività</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>get_Attivit.Esito__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Esito</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_Attivit.Sottoesito__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Sottoesito</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_Attivit.RevocationCC_Sent__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_Attivit.OptOput_Sent__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>updateActivity</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>AltroVantaggio</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;Altro Vantaggio&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Altro Vantaggio</stringValue>
        </value>
    </choices>
    <choices>
        <name>Concorrenza</name>
        <choiceText>Concorrenza</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Concorrenza</stringValue>
        </value>
    </choices>
    <choices>
        <name>Optout</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;Optout&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Optout</stringValue>
        </value>
    </choices>
    <choices>
        <name>Prezzo</name>
        <choiceText>Prezzo</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Prezzo</stringValue>
        </value>
    </choices>
    <choices>
        <name>Prodotto</name>
        <choiceText>Prodotto</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Prodotto</stringValue>
        </value>
    </choices>
    <choices>
        <name>UnsubscribeCC</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;Unsubscribe CC&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Unsubscribe CC</stringValue>
        </value>
    </choices>
    <choices>
        <name>valueAppuntamentoAgenzia</name>
        <choiceText>&lt;h5&gt;Appuntamento Agenzia&lt;/h5&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Appuntamento Agenzia</stringValue>
        </value>
    </choices>
    <choices>
        <name>valueDaRichiamareAgenzia</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;Da richiamare Agenzia&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Da richiamare Agenzia</stringValue>
        </value>
    </choices>
    <choices>
        <name>valueInteresseGenerico</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;Interesse generico&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Interesse generico</stringValue>
        </value>
    </choices>
    <choices>
        <name>valueNonInteressato</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;Non interessato&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Non interessato</stringValue>
        </value>
    </choices>
    <choices>
        <name>valueVendita</name>
        <choiceText>Vendita</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Vendita</stringValue>
        </value>
    </choices>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>check_inviata_revoca</name>
        <label>check inviata revoca</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Richiesta_OPT_OUT_CC</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>non inviata in precedenza</defaultConnectorLabel>
        <rules>
            <name>inviata_in_precedenza</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>get_Attivit.OptOput_Sent__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>revoca_gi_inviata</targetReference>
            </connector>
            <label>inviata in precedenza</label>
        </rules>
    </decisions>
    <decisions>
        <name>checkButton</name>
        <label>checkButton</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>OPT_OUT_CC</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>isAnnulla</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>buttonnext.renderNext</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <label>isAnnulla</label>
        </rules>
    </decisions>
    <decisions>
        <name>checkDecisionButton</name>
        <label>checkDecisionButton</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Copy_1_of_modifyEsitoAttivit</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>isConferma</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Copy_1_of_test.renderNext</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>modifyEsitoAttivit</targetReference>
            </connector>
            <label>isConferma</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_1_of_checkDecisionButton</name>
        <label>Copy 1 of checkDecisionButton</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Copy_1_of_modifyEsitoAttivit</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_1_of_isConferma</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>test.renderNext</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>OPT_OUT_CC_riepilogo</targetReference>
            </connector>
            <label>Copy 1 of isConferma</label>
        </rules>
    </decisions>
    <description>Con invio email da Flow e custom metadata</description>
    <environments>Default</environments>
    <formulas>
        <name>Ambiente</name>
        <dataType>String</dataType>
        <expression>IF(CONTAINS({!$Api.Enterprise_Server_URL_260}, &apos;sandbox&apos;), &apos;Sandbox&apos;, &apos;Production&apos;)</expression>
    </formulas>
    <formulas>
        <name>subjectEmail</name>
        <dataType>String</dataType>
        <expression>&quot;RICHIESTA OPT-OUT per cliente &quot; + {!getAccount.Name}</expression>
    </formulas>
    <interviewLabel>CC-OPT_OUT_Handler {!$Flow.CurrentDateTime}</interviewLabel>
    <label>CC-OPT_OUT_Handler</label>
    <loops>
        <name>loopEmail</name>
        <label>loopEmail</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>getMetadatoSendEmail</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>assignEmailToSend</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Invio_email</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>get_Attivit</name>
        <label>get Attività</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>getAccount</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getAccount</name>
        <label>getAccount</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>check_inviata_revoca</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>get_Attivit.AccountId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getMetadatoSendEmail</name>
        <label>getMetadatoSendEmail</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>loopEmail</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Label</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Ambiente</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>CC_Email_Config__mdt</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>updateActivity</name>
        <label>updateActivity</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>screenConfermaRevoca</targetReference>
        </connector>
        <inputReference>get_Attivit</inputReference>
    </recordUpdates>
    <screens>
        <name>OPT_OUT_CC</name>
        <label>OPT OUT CC</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Annulla</backButtonLabel>
        <connector>
            <targetReference>getMetadatoSendEmail</targetReference>
        </connector>
        <fields>
            <name>OPT_OUT_CC_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>OPT_OUT_CC_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>textEsitoFinaleChiamata</name>
                    <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;Vuoi aggiungerlo anche come esito finale della chiamata?&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>middle</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <fields>
            <name>test</name>
            <extensionName>runtime_industries_lending:flowFooter</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>buttonLabelCustom</name>
                <value>
                    <stringValue>Annulla</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>renderNext</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>buttonLabelNext</name>
                <value>
                    <stringValue>Conferma</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>buttonLabelPrevious</name>
                <value>
                    <stringValue>Previous</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>showPrevious</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>OPT_OUT_CC_riepilogo</name>
        <label>OPT OUT CC riepilogo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Annulla</backButtonLabel>
        <connector>
            <targetReference>checkDecisionButton</targetReference>
        </connector>
        <fields>
            <name>OPT_OUT_CC_riepilogo_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>OPT_OUT_CC_riepilogo_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Copy_1_of_textEsitoFinaleChiamata</name>
                    <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Conferma gli esiti per questa Attività &lt;a href=&quot;/lightning/r/Case/{!get_Attivit.Id}/view&quot; rel=&quot;noopener noreferrer&quot; target=&quot;_blank&quot; style=&quot;color: rgb(0, 0, 0); font-family: &amp;quot;Segoe UI VSS (Regular)&amp;quot;, &amp;quot;Segoe UI&amp;quot;, -apple-system, BlinkMacSystemFont, Roboto, &amp;quot;Helvetica Neue&amp;quot;, Helvetica, Ubuntu, Arial, sans-serif, &amp;quot;Apple Color Emoji&amp;quot;, &amp;quot;Segoe UI Emoji&amp;quot;, &amp;quot;Segoe UI Symbol&amp;quot;; font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;&lt;strong&gt;&lt;em&gt;&lt;u&gt;{!get_Attivit.CaseNumber}&lt;/u&gt;&lt;/em&gt;&lt;/strong&gt;&lt;/a&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <fields>
            <name>OPT_OUT_CC_riepilogo_Section2</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>OPT_OUT_CC_riepilogo_Section2_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Esito</name>
                    <choiceReferences>valueAppuntamentoAgenzia</choiceReferences>
                    <choiceReferences>valueDaRichiamareAgenzia</choiceReferences>
                    <choiceReferences>valueInteresseGenerico</choiceReferences>
                    <choiceReferences>valueNonInteressato</choiceReferences>
                    <choiceReferences>valueVendita</choiceReferences>
                    <dataType>String</dataType>
                    <defaultSelectedChoiceReference>valueNonInteressato</defaultSelectedChoiceReference>
                    <fieldText>Esito</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>OPT_OUT_CC_riepilogo_Section2_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Sottoesito</name>
                    <choiceReferences>UnsubscribeCC</choiceReferences>
                    <choiceReferences>Concorrenza</choiceReferences>
                    <choiceReferences>Optout</choiceReferences>
                    <choiceReferences>Prezzo</choiceReferences>
                    <choiceReferences>Prodotto</choiceReferences>
                    <choiceReferences>AltroVantaggio</choiceReferences>
                    <dataType>String</dataType>
                    <defaultSelectedChoiceReference>Optout</defaultSelectedChoiceReference>
                    <fieldText>Sottoesito</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <fields>
            <name>Copy_1_of_test</name>
            <extensionName>runtime_industries_lending:flowFooter</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>buttonLabelCustom</name>
                <value>
                    <stringValue>Annulla</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>renderNext</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>buttonLabelNext</name>
                <value>
                    <stringValue>Conferma</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>buttonLabelPrevious</name>
                <value>
                    <stringValue>Previous</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>showPrevious</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>revoca_gi_inviata</name>
        <label>revoca già inviata</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>revoca_gi_inviata_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>revoca_gi_inviata_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>textRevocaInviata</name>
                    <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;La richiesta di Revoca è stata gia inviata sul attività &lt;/span&gt;&lt;strong style=&quot;font-size: 14px;&quot;&gt;{!get_Attivit.CaseNumber}&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Richiesta_OPT_OUT_CC</name>
        <label>Richiesta OPT OUT CC</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Annulla</backButtonLabel>
        <connector>
            <targetReference>checkButton</targetReference>
        </connector>
        <fields>
            <name>Richiesta_OPT_OUT_CC_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Richiesta_OPT_OUT_CC_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>textInoltroRichiesta</name>
                    <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;Sei sicuro di voler inoltrare la richiesta di Opt Out all’ufficio revoca consenso?&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <styleProperties>
                        <verticalAlignment>
                            <stringValue>top</stringValue>
                        </verticalAlignment>
                        <width>
                            <stringValue>12</stringValue>
                        </width>
                    </styleProperties>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <fields>
            <name>buttonnext</name>
            <extensionName>runtime_industries_lending:flowFooter</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>buttonLabelNext</name>
                <value>
                    <stringValue>Conferma</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>buttonLabelPrevious</name>
                <value>
                    <stringValue>Annulla</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>showPrevious</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>buttonLabelCustom</name>
                <value>
                    <stringValue>Annulla</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>screenConfermaRevoca</name>
        <label>screenConfermaRevoca</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>ToastEventSuccess</name>
            <extensionName>c:cC_OpenToastEventIntoFlow</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>toastMessage</name>
                <value>
                    <stringValue>La richiesta è stata inoltrata correttamente</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>toastVariant</name>
                <value>
                    <stringValue>success</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <fields>
            <name>redirectLwc</name>
            <extensionName>c:cC_RedirectFlowRiassegnazione</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <nextOrFinishButtonLabel>Chiudi</nextOrFinishButtonLabel>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>get_Attivit</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>EmailToSend</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
