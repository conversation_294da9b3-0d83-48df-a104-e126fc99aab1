@IsTest
private class CaseCreationServiceTest {

    private static Account makeAccount(String name, String phone) {
        Account accountRecord = new Account(Name = name);
        if (phone != null) accountRecord.Phone = phone;
        insert accountRecord;
        return accountRecord;
    }

    private static Opportunity makeOpportunity(Account accountRecord, Id agencyId) {
        Opportunity opportunityRecord = new Opportunity();
        opportunityRecord.Name = 'Test Opportunity';
        opportunityRecord.AccountId = accountRecord.Id;
        opportunityRecord.StageName = 'Prospecting';
        opportunityRecord.CloseDate = Date.today().addDays(10);
        opportunityRecord.Agency__c = agencyId;
        insert opportunityRecord;
        return opportunityRecord;
    }

    @IsTest
    static void testCallMeBack_createsParentAndChild() {
        // Arrange
        Account customerAccount = makeAccount('Customer A', '**********');
        Account agencyAccount = makeAccount('Agency A', null);
        Opportunity opportunity = makeOpportunity(customerAccount, agencyAccount.Id);

        CaseCreationService.Context context = new CaseCreationService.Context();
        context.opportunityId = opportunity.Id;
        context.areaOfNeed = 'AreaX';
        context.stageName = 'Stage1';
        context.timeSlot = '9-12';
        context.notes = 'Please call me back';
        context.codDomainActivity = 'CMB01';
        context.numeroRicontatto = '**********';
        context.objectType = 'ATTIVITA_CONTATTO';
        context.eventType = 'RICHIESTA_CONTATTO';

        Test.startTest();
        CaseCreationService.Result result = CaseCreationService.createCases(context);
        Test.stopTest();

        System.assertNotEquals(null, result, 'Result should not be null');
        System.assertNotEquals(null, result.parentCaseId, 'Parent Case Id should be set');
        System.assertNotEquals(null, result.childCaseId, 'Child Case Id should be set');
        System.assertEquals('CallMeBack', result.caseType, 'Unexpected Case Type');

        Case parentCase = [SELECT Id, Type, Status, SuppliedPhone, RecordTypeId FROM Case WHERE Id = :result.parentCaseId];
        Case childCase = [SELECT Id, Type, Status, SuppliedPhone, RecordTypeId, ParentId FROM Case WHERE Id = :result.childCaseId];

        System.assertEquals('New', parentCase.Status, 'Parent Status');
        System.assertEquals('New', childCase.Status, 'Child Status');
        System.assertEquals('CallMeBack', parentCase.Type, 'Parent Type');
        System.assertEquals('CallMeBack', childCase.Type, 'Child Type');
        System.assertEquals(parentCase.Id, childCase.ParentId, 'Child must reference Parent');
        System.assertEquals('**********', parentCase.SuppliedPhone, 'Phone mapping');
    }

    @IsTest
    static void testPhoneFallback_toAccountPhone() {
        // Arrange
        Account customerAccount = makeAccount('Customer B', '**********');
        Account agencyAccount = makeAccount('Agency B', null);
        Opportunity opportunity = makeOpportunity(customerAccount, agencyAccount.Id);

        CaseCreationService.Context context = new CaseCreationService.Context();
        context.opportunityId = opportunity.Id;
        // Leave numeroRicontatto null to trigger fallback
        context.objectType = 'ATTIVITA_CONTATTO';
        context.eventType = 'RICHIESTA_CONTATTO';

        Test.startTest();
        CaseCreationService.Result result = CaseCreationService.createCases(context);
        Test.stopTest();

        System.assertNotEquals(null, result, 'Result should not be null');

        Case parentCase = [SELECT Id, SuppliedPhone FROM Case WHERE Id = :result.parentCaseId];
        System.assertEquals('**********', parentCase.SuppliedPhone, 'Fallback to Account.Phone failed');
    }

    @IsTest
    static void testNonCaseScenario_returnsNullNoInsert() {
        Account customerAccount = makeAccount('Customer C', '**********');
        Account agencyAccount = makeAccount('Agency C', null);
        Opportunity opportunity = makeOpportunity(customerAccount, agencyAccount.Id);

        CaseCreationService.Context context = new CaseCreationService.Context();
        context.opportunityId = opportunity.Id;
        context.objectType = 'SOMETHING_ELSE';
        context.eventType = 'NONE';

        Test.startTest();
        CaseCreationService.Result result = CaseCreationService.createCases(context);
        Test.stopTest();

        System.assertEquals(null, result, 'Should return null for non-case scenarios');

        Integer createdCasesCount = [SELECT COUNT() FROM Case WHERE AccountId = :customerAccount.Id];
        System.assertEquals(0, createdCasesCount, 'No cases should be created');
    }

    @IsTest
    static void testDirectCaseType_withFlatActivity() {
        // Arrange
        Account customerAccount = makeAccount('Customer D', '**********');
        Account agencyAccount = makeAccount('Agency D', null);
        Opportunity opportunity = makeOpportunity(customerAccount, agencyAccount.Id);

        CaseCreationService.Context context = new CaseCreationService.Context();
        context.opportunityId = opportunity.Id;
        context.caseType = 'CallMeBack';
        // flat activity
        context.areaOfNeed = 'AreaFlat';
        context.stageName = 'FlatStage';
        context.timeSlot = '15-18';
        context.notes = 'Flat notes';
        context.codDomainActivity = 'FLAT1';
        context.numeroRicontatto = '**********';

        Test.startTest();
        CaseCreationService.Result result = CaseCreationService.createCases(context);
        Test.stopTest();

        System.assertNotEquals(null, result, 'Result should not be null');
        Case parentCase = [SELECT Id, Type, Status, SuppliedPhone, Description FROM Case WHERE Id = :result.parentCaseId];
        System.assertEquals('CallMeBack', parentCase.Type, 'Type should match explicit caseType');
        System.assertEquals('**********', parentCase.SuppliedPhone, 'Phone from flat activity');
        System.assertEquals('Flat notes', parentCase.Description, 'Notes from flat activity');
    }

    @IsTest
    static void testChangeEvent_newPriorityGreater_updatesRootAndChild() {
        // Initial setup: create a CallMeBack parent/child
        Account customerAccount = makeAccount('Customer E', '**********');
        Account agencyAccount = makeAccount('Agency E', null);
        Opportunity opportunity = makeOpportunity(customerAccount, agencyAccount.Id);

        CaseCreationService.Context ctx1 = new CaseCreationService.Context();
        ctx1.opportunityId = opportunity.Id;
        ctx1.objectType = 'ATTIVITA_CONTATTO';
        ctx1.eventType = 'RICHIESTA_CONTATTO'; // -> CallMeBack (priority 1)
        CaseCreationService.Result first = CaseCreationService.createCases(ctx1);
        System.assertNotEquals(null, first, 'First creation must succeed');

        // Change event: new type with greater numeric priority (Salva Preventivo -> 3)
        CaseCreationService.Context ctx2 = new CaseCreationService.Context();
        ctx2.opportunityId = opportunity.Id;
        ctx2.objectType = 'TRATTATIVA';
        ctx2.eventType = 'SALVA_PREVENTIVO'; // -> Salva Preventivo (priority 3)

        Test.startTest();
        CaseCreationService.Result second = CaseCreationService.createCases(ctx2);
        Test.stopTest();

        System.assertNotEquals(null, second, 'Update path should return a result');
        System.assertEquals(first.parentCaseId, second.parentCaseId, 'Root case should be updated in place');

        Case updatedRoot = [SELECT Id, Type FROM Case WHERE Id = :second.parentCaseId];
        System.assertEquals('Salva Preventivo', updatedRoot.Type, 'Root Type updated to new event');

        List<Case> children = [SELECT Id, Type, ParentId FROM Case WHERE ParentId = :updatedRoot.Id];
        System.assertEquals(1, children.size(), 'Exactly one child should exist after replace');
        System.assertEquals('Salva Preventivo', children[0].Type, 'Child Type should match new event');
    }

    @IsTest
    static void testChangeEvent_newPriorityLowerOrEqual_noAction() {
        // Initial: create Salva Preventivo (priority 3)
        Account customerAccount = makeAccount('Customer F', '**********');
        Account agencyAccount = makeAccount('Agency F', null);
        Opportunity opportunity = makeOpportunity(customerAccount, agencyAccount.Id);

        CaseCreationService.Context ctx1 = new CaseCreationService.Context();
        ctx1.opportunityId = opportunity.Id;
        ctx1.objectType = 'TRATTATIVA';
        ctx1.eventType = 'SALVA_PREVENTIVO';
        CaseCreationService.Result first = CaseCreationService.createCases(ctx1);
        System.assertNotEquals(null, first, 'First creation must succeed');

        Integer beforeChildren = [SELECT COUNT() FROM Case WHERE ParentId = :first.parentCaseId];
        Case rootBefore = [SELECT Id, Type FROM Case WHERE Id = :first.parentCaseId];

        // Change event: new type with lower/equal numeric priority (CallMeBack -> 1)
        CaseCreationService.Context ctx2 = new CaseCreationService.Context();
        ctx2.opportunityId = opportunity.Id;
        ctx2.objectType = 'ATTIVITA_CONTATTO';
        ctx2.eventType = 'RICHIESTA_CONTATTO';

        Test.startTest();
        CaseCreationService.Result second = CaseCreationService.createCases(ctx2);
        Test.stopTest();

        System.assertEquals(null, second, 'No action expected for lower/equal priority');

        Integer afterChildren = [SELECT COUNT() FROM Case WHERE ParentId = :first.parentCaseId];
        Case rootAfter = [SELECT Id, Type FROM Case WHERE Id = :first.parentCaseId];
        System.assertEquals(beforeChildren, afterChildren, 'No child change expected');
        System.assertEquals(rootBefore.Type, rootAfter.Type, 'Root Type unchanged');
    }
}
